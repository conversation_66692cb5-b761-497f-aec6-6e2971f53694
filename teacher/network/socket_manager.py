# teacher/network/socket_manager.py
import socketio
import requests
import urllib3
from PyQt5.QtCore import QObject, pyqtSignal

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class SocketIOManager(QObject):
    answer_submitted_signal = pyqtSignal(dict)
    help_request_received_signal = pyqtSignal(dict)
    help_request_canceled_signal = pyqtSignal(dict)
    timer_started_signal = pyqtSignal(dict)
    group_topic_received_signal = pyqtSignal(dict)
    student_drawing_received_signal = pyqtSignal(dict)
    chat_message_received_signal = pyqtSignal(dict)
    # 触控回传相关信号
    touch_control_response_signal = pyqtSignal(dict)
    virtual_keyboard_status_signal = pyqtSignal(dict)
    touch_control_error_signal = pyqtSignal(dict)
    group_drawing_signal = pyqtSignal(dict)
    whiteboard_state_from_group_signal = pyqtSignal(dict)
    
    def __init__(self, api_url):
        super().__init__()
        # 配置HTTP会话以支持HTTPS
        http_session = requests.Session()
        http_session.verify = False  # 禁用SSL证书验证

        # 配置SocketIO客户端，增加重连设置
        self.sio = socketio.Client(
            http_session=http_session,
            reconnection=True,
            reconnection_attempts=5,
            reconnection_delay=1,
            reconnection_delay_max=5
        )
        self.api_url = api_url
        self.token = None
        self.register_handlers()

    def register_handlers(self):
        @self.sio.event
        def connect():
            print("Socket.IO 已连接")
            # 连接成功后，立即发送认证请求
            if self.token:
                print("正在发送认证请求...")
                self.sio.emit('authenticate', {'token': self.token})

        @self.sio.on('authenticated')
        def on_authenticated(data):
            """处理认证结果"""
            if data.get('success'):
                print("Socket.IO 认证成功")
                # 认证成功后，如果有待处理的课程ID，则加入房间
                if self.current_course_id:
                    self.join_course(self.current_course_id)
            else:
                print(f"Socket.IO 认证失败: {data.get('error')}")

        @self.sio.event
        def disconnect():
            print("Socket.IO 已断开")

        @self.sio.event
        def connect_error(data):
            print(f"Socket.IO 连接错误: {data}")

        @self.sio.event
        def reconnect():
            print("Socket.IO 重新连接成功")
            # 重新连接后重新认证
            if self.token:
                print("重新连接后发送认证请求...")
                self.sio.emit('authenticate', {'token': self.token})

        @self.sio.on('chat_message')
        def on_chat_message(data):
            """接收聊天消息"""
            print(f"收到 'chat_message' 事件: {data}")
            self.chat_message_received_signal.emit(data)

        @self.sio.on('answer_submitted')
        def on_answer_submitted(data):
            print(f"收到 'answer_submitted' 事件: {data}")
            self.answer_submitted_signal.emit(data)

        @self.sio.on('help_request_from_group')
        def on_help_request_from_group(data):
            print(f"收到求助请求: {data}")
            self.help_request_received_signal.emit(data)

        @self.sio.on('help_request_cancelled')
        def on_help_request_cancelled(data):
            print(f"收到取消求助请求: {data}")
            self.help_request_canceled_signal.emit(data)

        @self.sio.on('start_timer')
        def on_start_timer(data):
            print(f"收到 'start_timer' 事件: {data}")
            self.timer_started_signal.emit(data)

        @self.sio.on('new_group_topic')
        def on_new_group_topic(data):
            print(f"收到 'new_group_topic' 事件: {data}")
            self.group_topic_received_signal.emit(data)
            
        @self.sio.on('student_answer')
        def on_student_answer(data):
            print(f"收到学生答题数据: {data}")
            self.answer_submitted_signal.emit(data)

        @self.sio.on('new_student_drawing')
        def on_new_student_drawing(data):
            print(f"收到学生绘图数据: {data}")
            self.student_drawing_received_signal.emit(data)

        # 触控回传相关事件处理
        @self.sio.on('touch_control_response')
        def on_touch_control_response(data):
            print(f"收到触控控制响应: {data}")
            self.touch_control_response_signal.emit(data)

        @self.sio.on('virtual_keyboard_status')
        def on_virtual_keyboard_status(data):
            print(f"收到虚拟键盘状态: {data}")
            self.virtual_keyboard_status_signal.emit(data)

        @self.sio.on('touch_control_error')
        def on_touch_control_error(data):
            print(f"收到触控控制错误: {data}")
            self.touch_control_error_signal.emit(data)

        @self.sio.on('group_drawing_to_teacher')
        def on_group_drawing(data):
            self.group_drawing_signal.emit(data)

        @self.sio.on('whiteboard_state_from_group')
        def on_whiteboard_state_from_group(data):
            self.whiteboard_state_from_group_signal.emit(data)

    def connect(self, token=None, course_id=None):
        self.token = token
        # 存储 course_id，但连接后等待认证成功再加入
        self.current_course_id = course_id
        headers = {'Authorization': f'Bearer {self.token}'} if self.token else {}

        try:
            print(f"正在连接到 Socket.IO 服务器: {self.api_url}")
            # connect() 调用将触发在 register_handlers 中定义的 connect 事件
            self.sio.connect(
                self.api_url,
                headers=headers,
                wait_timeout=30,  # 等待连接超时时间
                transports=['websocket', 'polling']  # 指定传输方式
            )
            print("Socket.IO 连接成功")
        except socketio.exceptions.ConnectionError as e:
            print(f"Socket.IO 连接失败: {e}")
            print(f"连接URL: {self.api_url}")
            print("提示：请检查服务器是否正在运行，网络连接是否正常")
            # 可以在这里添加重试逻辑或者通知UI
        except Exception as e:
            print(f"Socket.IO 连接发生未知错误: {e}")
            print(f"错误类型: {type(e).__name__}")

    def join_course(self, course_id):
        """加入课程房间"""
        if self.sio.connected:
            self.current_course_id = course_id
            self.sio.emit('join_course', {'course_id': course_id})
            print(f"已加入课程房间: {course_id}")

    def disconnect(self):
        self.sio.disconnect()

    def is_connected(self):
        return self.sio.connected

    def emit_whiteboard_sync(self, data):
        """发送白板同步数据"""
        if self.is_connected():
            self.sio.emit('whiteboard_sync', data)