# teacher/ui/widgets.py
import vlc
import json
import qrcode
import io
import time
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit,
                            QPushButton, QMessageBox, QListWidget, QFrame)
from PyQt5.QtCore import pyqtSignal, Qt, QTimer
from PyQt5.QtGui import QPalette, QColor, QPixmap




class DraggableListWidget(QListWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragDropMode(QListWidget.DragDrop)
        self.setDefaultDropAction(Qt.MoveAction)
        self.setSelectionMode(QListWidget.ExtendedSelection)

class VideoFrame(QFrame):
    double_clicked = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFrameShape(QFrame.Box)
        self.setMinimumSize(240, 135)
        self.setMaximumSize(480, 270)
        palette = self.palette()
        palette.setColor(QPalette.Window, QColor(0,0,0))
        self.setPalette(palette)
        self.setAutoFillBackground(True)
        self.vlc_instance = vlc.Instance("--no-xlib")
        self.media_player = self.vlc_instance.media_player_new()
        self.media_player.set_xwindow(int(self.winId()))

    def mouseDoubleClickEvent(self, event):
        self.double_clicked.emit()
        super().mouseDoubleClickEvent(event)

    def play(self, url):
        media = self.vlc_instance.media_new(url)
        media.add_option('network-caching=1000')
        self.media_player.set_media(media)
        self.media_player.play()

        # 立即设置一次宽高比，以处理主屏幕等预先创建的窗口
        w = self.width()
        h = self.height()
        if h > 0:
            self.media_player.video_set_aspect_ratio(f"{w}:{h}")

    def resizeEvent(self, event):
        """在控件大小改变时，自动更新视频宽高比以填充。"""
        super().resizeEvent(event)
        w = self.width()
        h = self.height()
        # 播放期间，因窗口缩放再次调整
        if self.media_player.is_playing() and h > 0:
            self.media_player.video_set_aspect_ratio(f"{w}:{h}")

    def stop(self):
        self.media_player.stop()

class LoginDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("教师登录")
        self.setFixedSize(350, 250)

        layout = QVBoxLayout(self)

        # 标题
        title_label = QLabel("教师端登录")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 20px;")
        layout.addWidget(title_label)

        # 用户名
        self.username_label = QLabel("用户名:")
        self.username_input = QLineEdit()
        self.username_input.setText("T001")  # 测试
        self.username_input.setPlaceholderText("请输入用户名")

        # 密码
        self.password_label = QLabel("密码:")
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setText("123456")  # 测试
        self.password_input.setPlaceholderText("请输入密码")

        # 按钮布局
        button_layout = QHBoxLayout()

        # 登录按钮
        self.login_button = QPushButton("登录")
        self.login_button.clicked.connect(self.accept)
        self.login_button.setStyleSheet("""
            QPushButton {
                background-color: #009688;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #00796b;
            }
        """)

        # 二维码登录按钮
        self.qr_button = QPushButton("二维码登录")
        self.qr_button.clicked.connect(self.show_qr_login)
        self.qr_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)

        button_layout.addWidget(self.login_button)
        button_layout.addWidget(self.qr_button)

        layout.addWidget(self.username_label)
        layout.addWidget(self.username_input)
        layout.addWidget(self.password_label)
        layout.addWidget(self.password_input)
        layout.addLayout(button_layout)

    def show_qr_login(self):
        """显示二维码登录窗口"""
        qr_dialog = QRLoginDialog(self)
        if qr_dialog.exec_() == QDialog.Accepted:
            # 如果二维码登录成功，关闭主登录窗口
            self.accept()

    def get_credentials(self):
        """获取登录凭据"""
        return self.username_input.text(), self.password_input.text()


class QRLoginDialog(QDialog):
    """二维码登录对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("二维码登录")
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint)

        self.setup_ui()
        self.generate_login_qr()

        # 定时刷新二维码（每30秒）
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.generate_login_qr)
        self.refresh_timer.start(30000)  # 30秒刷新一次

    def setup_ui(self):
        """设置界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(20)
        layout.setContentsMargins(30, 30, 30, 30)

        # 标题
        title_label = QLabel("扫码登录")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #333; margin-bottom: 20px;")
        layout.addWidget(title_label)

        # 二维码显示区域
        self.qr_code_label = QLabel()
        self.qr_code_label.setFixedSize(300, 300)
        self.qr_code_label.setStyleSheet("""
            border: 2px solid #e0e0e0;
            background: white;
            border-radius: 10px;
        """)
        self.qr_code_label.setAlignment(Qt.AlignCenter)
        self.qr_code_label.setText("正在生成二维码...")
        layout.addWidget(self.qr_code_label, 0, Qt.AlignCenter)

        # 状态标签
        self.status_label = QLabel("二维码有效期：30秒")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #999; font-size: 12px;")
        layout.addWidget(self.status_label)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 刷新按钮
        refresh_btn = QPushButton("刷新二维码")
        refresh_btn.clicked.connect(self.generate_login_qr)
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        button_layout.addWidget(refresh_btn)

        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.reject)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        button_layout.addWidget(close_btn)

        layout.addLayout(button_layout)

    def generate_login_qr(self):
        """生成登录二维码"""
        try:
            # 生成登录二维码数据（使用临时的教师ID）
            qr_data = {
                "type": "client_login",
                "teacher_id": "temp_teacher",  # 临时教师ID，实际使用时会被替换
                "token": "temp_token",  # 临时token，实际使用时会被替换
                "timestamp": int(time.time()),
                "expires_in": 300  # 5分钟有效期
            }

            # 生成二维码
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(json.dumps(qr_data))
            qr.make(fit=True)

            # 创建二维码图片
            img = qr.make_image(fill_color="black", back_color="white")

            # 转换为QPixmap
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            buffer.seek(0)

            pixmap = QPixmap()
            pixmap.loadFromData(buffer.getvalue())

            # 缩放到合适大小
            scaled_pixmap = pixmap.scaled(280, 280, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.qr_code_label.setPixmap(scaled_pixmap)

            self.status_label.setText("二维码已生成，请在网页端扫描")

        except ImportError:
            self.qr_code_label.setText("缺少二维码生成库\n请安装 qrcode 和 Pillow")
            self.status_label.setText("错误：缺少必要的库")
        except Exception as e:
            self.qr_code_label.setText(f"生成失败\n{str(e)}")
            self.status_label.setText("错误：生成二维码失败")

    def closeEvent(self, event):
        """关闭事件"""
        if self.refresh_timer:
            self.refresh_timer.stop()
        super().closeEvent(event)
