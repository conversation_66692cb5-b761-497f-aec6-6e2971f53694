#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试白板服务的脚本
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from app import create_app
from app.services.whiteboard_service import whiteboard_service
from app.models.database import get_db

def test_whiteboard_service():
    """测试白板服务"""
    print("开始测试白板服务...")
    
    # 测试参数
    group_id = "412462e9-b42f-47ca-acd8-3b0200ce0ee3"
    course_schedule_id = "CS_001"
    student_id = "S001"
    session_name = "测试白板会话"
    
    print(f"测试参数:")
    print(f"  group_id: {group_id}")
    print(f"  course_schedule_id: {course_schedule_id}")
    print(f"  student_id: {student_id}")
    print(f"  session_name: {session_name}")
    
    # 验证数据库中的数据
    print("\n验证数据库数据...")
    conn = get_db()
    cursor = conn.cursor()
    
    # 检查分组
    cursor.execute("SELECT id, group_name, course_schedule_id FROM class_groups WHERE id = ?", (group_id,))
    group = cursor.fetchone()
    print(f"分组信息: {dict(group) if group else 'Not Found'}")
    
    # 检查课程安排
    cursor.execute("SELECT id, status FROM course_schedules WHERE id = ?", (course_schedule_id,))
    course = cursor.fetchone()
    print(f"课程安排: {dict(course) if course else 'Not Found'}")
    
    # 检查学生
    cursor.execute("SELECT student_id, name FROM students WHERE student_id = ?", (student_id,))
    student = cursor.fetchone()
    print(f"学生信息: {dict(student) if student else 'Not Found'}")
    
    conn.close()
    
    # 测试创建会话
    print("\n测试创建白板会话...")
    session_id = whiteboard_service.create_session(
        group_id, course_schedule_id, session_name, student_id
    )
    
    if session_id:
        print(f"✅ 成功创建会话: {session_id}")
        
        # 测试加入会话
        print("\n测试加入会话...")
        success = whiteboard_service.join_session(session_id, student_id, 'student', '张三')
        if success:
            print("✅ 成功加入会话")
            
            # 测试获取参与者
            participants = whiteboard_service.get_session_participants(session_id)
            print(f"参与者列表: {participants}")
            
            # 测试保存内容
            print("\n测试保存内容...")
            test_elements = [{"id": "test1", "type": "rectangle", "x": 100, "y": 100}]
            test_app_state = {"viewBackgroundColor": "#ffffff"}
            
            save_success = whiteboard_service.save_content(session_id, test_elements, test_app_state, student_id)
            if save_success:
                print("✅ 成功保存内容")
                
                # 测试获取内容
                content = whiteboard_service.get_session_content(session_id)
                print(f"获取的内容: {content}")
            else:
                print("❌ 保存内容失败")
        else:
            print("❌ 加入会话失败")
    else:
        print("❌ 创建会话失败")

if __name__ == "__main__":
    # 创建应用上下文
    app, _ = create_app()
    with app.app_context():
        test_whiteboard_service()
