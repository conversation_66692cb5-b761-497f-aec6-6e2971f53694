#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试白板状态的脚本
"""

import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from app import create_app
from app.services.whiteboard_service import whiteboard_service
from app.models.database import get_db

def test_whiteboard_status():
    """测试白板状态"""
    print("🎯 白板协同功能状态检查")
    print("=" * 50)
    
    # 检查活跃会话
    print("📋 活跃的白板会话:")
    conn = get_db()
    cursor = conn.cursor()
    
    cursor.execute("""
        SELECT ws.id, ws.session_name, ws.status, ws.collaboration_enabled,
               cg.group_name, COUNT(wp.user_id) as participant_count
        FROM whiteboard_sessions ws
        LEFT JOIN class_groups cg ON ws.group_id = cg.id
        LEFT JOIN whiteboard_participants wp ON ws.id = wp.session_id AND wp.is_online = 1
        WHERE ws.status = 'active'
        GROUP BY ws.id
    """)
    
    sessions = cursor.fetchall()
    
    if sessions:
        for session in sessions:
            print(f"  📝 {session['session_name']}")
            print(f"     会话ID: {session['id'][:8]}...")
            print(f"     分组: {session['group_name']}")
            print(f"     协作状态: {'✅ 开启' if session['collaboration_enabled'] else '❌ 关闭'}")
            print(f"     在线人数: {session['participant_count']}")
            print()
    else:
        print("  ❌ 没有活跃的白板会话")
    
    # 检查参与者
    print("👥 在线参与者:")
    cursor.execute("""
        SELECT wp.session_id, wp.user_name, wp.user_type, wp.role,
               ws.session_name
        FROM whiteboard_participants wp
        JOIN whiteboard_sessions ws ON wp.session_id = ws.id
        WHERE wp.is_online = 1
        ORDER BY ws.session_name, wp.user_name
    """)
    
    participants = cursor.fetchall()
    
    if participants:
        current_session = None
        for participant in participants:
            if current_session != participant['session_name']:
                current_session = participant['session_name']
                print(f"  📝 {current_session}:")
            
            role_icon = "👨‍🏫" if participant['user_type'] == 'teacher' else "👨‍🎓"
            print(f"     {role_icon} {participant['user_name']} ({participant['role']})")
    else:
        print("  ❌ 没有在线参与者")
    
    # 检查白板内容
    print("\n📄 白板内容:")
    cursor.execute("""
        SELECT ws.session_name, COUNT(wc.id) as content_versions,
               MAX(wc.created_at) as last_update
        FROM whiteboard_sessions ws
        LEFT JOIN whiteboard_contents wc ON ws.id = wc.session_id
        WHERE ws.status = 'active'
        GROUP BY ws.id
    """)
    
    contents = cursor.fetchall()
    
    if contents:
        for content in contents:
            print(f"  📝 {content['session_name']}")
            print(f"     版本数: {content['content_versions']}")
            print(f"     最后更新: {content['last_update'] or '无'}")
            print()
    else:
        print("  ❌ 没有白板内容")
    
    # 检查操作记录
    print("📊 最近操作:")
    cursor.execute("""
        SELECT wo.operation_type, wo.user_id, wo.created_at,
               ws.session_name
        FROM whiteboard_operations wo
        JOIN whiteboard_sessions ws ON wo.session_id = ws.id
        ORDER BY wo.created_at DESC
        LIMIT 5
    """)
    
    operations = cursor.fetchall()
    
    if operations:
        for op in operations:
            print(f"  🔄 {op['operation_type']} by {op['user_id']} in {op['session_name']}")
            print(f"     时间: {op['created_at']}")
    else:
        print("  ❌ 没有操作记录")
    
    conn.close()
    
    print("\n" + "=" * 50)
    print("✅ 白板协同功能状态检查完成")
    
    # 功能状态总结
    active_sessions = len(sessions)
    online_users = len(participants)
    
    print(f"📈 总结:")
    print(f"   活跃会话: {active_sessions}")
    print(f"   在线用户: {online_users}")
    print(f"   系统状态: {'🟢 正常运行' if active_sessions > 0 else '🟡 待激活'}")

if __name__ == "__main__":
    # 创建应用上下文
    app, _ = create_app()
    with app.app_context():
        test_whiteboard_status()
