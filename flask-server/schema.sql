-- 教室表
CREATE TABLE IF NOT EXISTS classrooms (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    capacity INTEGER NOT NULL,
    location TEXT,
    created_at TEXT NOT NULL
);

-- 弹幕表 - 通用消息表
CREATE TABLE IF NOT EXISTS danmaku (
    id TEXT PRIMARY KEY,
    course_schedule_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    user_type TEXT NOT NULL, -- 'student' or 'teacher'
    messages TEXT NOT NULL,
    message_count INTEGER DEFAULT 0,
    first_message_time TEXT,
    last_message_time TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    UNIQUE(course_schedule_id, user_id),
    FOREIGN KEY (course_schedule_id) REFERENCES course_schedules(id)
);

-- 班级表
CREATE TABLE IF NOT EXISTS classes (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    grade TEXT NOT NULL,
    created_at TEXT NOT NULL
);

-- 课程表
CREATE TABLE IF NOT EXISTS courses (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    code TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at TEXT NOT NULL
);

-- 教师表
CREATE TABLE IF NOT EXISTS teachers (
    teacher_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    password TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    created_at TEXT NOT NULL
);

-- 学生表
CREATE TABLE IF NOT EXISTS students (
    student_id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    password TEXT NOT NULL,
    gender TEXT NOT NULL,
    class_id TEXT NOT NULL,
    email TEXT,
    phone TEXT,
    created_at TEXT NOT NULL,
    FOREIGN KEY (class_id) REFERENCES classes(id)
);

-- 课程安排表
CREATE TABLE IF NOT EXISTS course_schedules (
    id TEXT PRIMARY KEY,
    course_id TEXT NOT NULL,
    teacher_id TEXT NOT NULL,
    classroom_id TEXT NOT NULL,
    class_id TEXT NOT NULL,
    day_of_week INTEGER NOT NULL,
    start_time TEXT NOT NULL,
    end_time TEXT NOT NULL,
    status TEXT DEFAULT 'scheduled',
    start_datetime TEXT,
    description TEXT,
    created_at TEXT NOT NULL,
    FOREIGN KEY (course_id) REFERENCES courses(id),
    FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id),
    FOREIGN KEY (classroom_id) REFERENCES classrooms(id),
    FOREIGN KEY (class_id) REFERENCES classes(id)
);

-- 考勤表
CREATE TABLE IF NOT EXISTS class_attendance (
    id TEXT PRIMARY KEY,
    course_schedule_id TEXT NOT NULL,
    student_id TEXT NOT NULL,
    signin_time TEXT,
    status TEXT DEFAULT 'absent',
    remark TEXT,
    created_at TEXT NOT NULL,
    FOREIGN KEY (course_schedule_id) REFERENCES course_schedules(id),
    FOREIGN KEY (student_id) REFERENCES students(student_id)
);

-- 作业表
CREATE TABLE IF NOT EXISTS homework (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    teacher_id TEXT NOT NULL,
    course_schedule_id TEXT,
    created_at TEXT NOT NULL,
    data TEXT NOT NULL,
    status TEXT DEFAULT 'draft',
    deadline TEXT,
    type TEXT DEFAULT 'homework',
    question_count INTEGER DEFAULT 0,
    submitted_count INTEGER DEFAULT 0,
    total_students INTEGER DEFAULT 0,
    FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id),
    FOREIGN KEY (course_schedule_id) REFERENCES course_schedules(id)
);

-- 试卷表（资源库用）
CREATE TABLE IF NOT EXISTS papers (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    teacher_id TEXT NOT NULL,
    created_at TEXT NOT NULL,
    data TEXT NOT NULL,
    FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id)
);

-- 作业成绩表
CREATE TABLE IF NOT EXISTS homework_results (
    id TEXT PRIMARY KEY,
    homework_id TEXT NOT NULL,
    student_id TEXT NOT NULL,
    score REAL NOT NULL,
    data TEXT NOT NULL,
    submitted_at TEXT NOT NULL,
    answers TEXT,
    FOREIGN KEY (homework_id) REFERENCES homework(id),
    FOREIGN KEY (student_id) REFERENCES students(student_id)
);

-- 文件夹表
CREATE TABLE IF NOT EXISTS t_folders (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    folder_name VARCHAR(255) NOT NULL,
    parent_id INTEGER DEFAULT NULL,
    teacher_id INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES t_folders(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id)
);

-- 文件表
CREATE TABLE IF NOT EXISTS t_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL UNIQUE,
    file_path VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100),
    folder_id INTEGER DEFAULT NULL,
    teacher_id INTEGER NOT NULL,
    course_schedule_id TEXT,
    upload_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (folder_id) REFERENCES t_folders(id) ON DELETE SET NULL,
    FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id),
    FOREIGN KEY (course_schedule_id) REFERENCES course_schedules(id)
);

-- 课堂分组表
CREATE TABLE IF NOT EXISTS class_groups (
    id TEXT PRIMARY KEY,
    course_schedule_id TEXT NOT NULL,
    group_name TEXT NOT NULL,
    group_description TEXT,
    created_by TEXT NOT NULL,
    created_at TEXT NOT NULL,
    FOREIGN KEY (course_schedule_id) REFERENCES course_schedules(id),
    FOREIGN KEY (created_by) REFERENCES teachers(teacher_id)
);

-- 分组成员表
CREATE TABLE IF NOT EXISTS group_members (
    id TEXT PRIMARY KEY,
    group_id TEXT NOT NULL,
    student_id TEXT NOT NULL,
    joined_at TEXT NOT NULL,
    UNIQUE(group_id, student_id),
    FOREIGN KEY (group_id) REFERENCES class_groups(id),
    FOREIGN KEY (student_id) REFERENCES students(student_id)
);

-- 习题表
CREATE TABLE IF NOT EXISTS exercises (
    id TEXT PRIMARY KEY,
    teacher_id TEXT NOT NULL,
    type TEXT NOT NULL,
    question TEXT NOT NULL,
    options TEXT,
    answer TEXT NOT NULL,
    difficulty TEXT NOT NULL,
    folder_path TEXT DEFAULT '/',
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id)
);

-- 课堂报告表
CREATE TABLE IF NOT EXISTS class_reports (
    id TEXT PRIMARY KEY,
    course_schedule_id TEXT NOT NULL,
    teacher_id TEXT NOT NULL,
    report_date TEXT NOT NULL,
    attendance_count INTEGER DEFAULT 0,
    total_students INTEGER DEFAULT 0,
    group_count INTEGER DEFAULT 0,
    homework_count INTEGER DEFAULT 0,
    report_content TEXT,
    remarks TEXT,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    FOREIGN KEY (course_schedule_id) REFERENCES course_schedules(id),
    FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id)
);

-- 白板会话表
CREATE TABLE IF NOT EXISTS whiteboard_sessions (
    id TEXT PRIMARY KEY,
    group_id TEXT NOT NULL,
    course_schedule_id TEXT NOT NULL,
    session_name TEXT NOT NULL,
    created_by TEXT NOT NULL,
    status TEXT DEFAULT 'active',
    collaboration_enabled BOOLEAN DEFAULT 1,
    max_participants INTEGER DEFAULT 50,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    ended_at TEXT,
    FOREIGN KEY (group_id) REFERENCES class_groups(id),
    FOREIGN KEY (course_schedule_id) REFERENCES course_schedules(id),
    FOREIGN KEY (created_by) REFERENCES teachers(teacher_id)
);

-- 白板内容表
CREATE TABLE IF NOT EXISTS whiteboard_contents (
    id TEXT PRIMARY KEY,
    session_id TEXT NOT NULL,
    content_data TEXT NOT NULL,
    app_state TEXT,
    version INTEGER DEFAULT 1,
    created_by TEXT,
    created_at TEXT NOT NULL,
    is_snapshot BOOLEAN DEFAULT 0,
    FOREIGN KEY (session_id) REFERENCES whiteboard_sessions(id)
);

-- 白板操作记录表
CREATE TABLE IF NOT EXISTS whiteboard_operations (
    id TEXT PRIMARY KEY,
    session_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    user_type TEXT NOT NULL,
    operation_type TEXT NOT NULL,
    operation_data TEXT NOT NULL,
    timestamp INTEGER NOT NULL,
    sequence_number INTEGER NOT NULL,
    created_at TEXT NOT NULL,
    FOREIGN KEY (session_id) REFERENCES whiteboard_sessions(id)
);

-- 白板参与者表
CREATE TABLE IF NOT EXISTS whiteboard_participants (
    id TEXT PRIMARY KEY,
    session_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    user_type TEXT NOT NULL,
    user_name TEXT NOT NULL,
    role TEXT DEFAULT 'editor',
    joined_at TEXT NOT NULL,
    last_active_at TEXT,
    left_at TEXT,
    is_online BOOLEAN DEFAULT 1,
    UNIQUE(session_id, user_id),
    FOREIGN KEY (session_id) REFERENCES whiteboard_sessions(id)
);
