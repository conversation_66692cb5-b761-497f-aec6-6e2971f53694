开始执行白板清理任务 - 2025-07-18 18:39:38.291986
清理不活跃会话失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个不活跃的白板会话
清理离线用户失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个离线用户记录
清理操作记录失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 条过期操作记录
清理内容版本失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个过期内容版本
数据库优化失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
白板清理任务完成 - 2025-07-18 18:39:38.292054
白板清理任务已启动，清理间隔: 1 小时
客户端已连接: MvnSu2CSytheGaUbAAAB 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
验证白板会话参数: group_id=412462e9-b42f-47ca-acd8-3b0200ce0ee3, course_schedule_id=CS_001, created_by=S001
创建白板会话失败: FOREIGN KEY constraint failed
客户端已断开: MvnSu2CSytheGaUbAAAB
客户端已连接: Bk9CG8aJcS0uojrwAAAD 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
验证白板会话参数: group_id=412462e9-b42f-47ca-acd8-3b0200ce0ee3, course_schedule_id=CS_001, created_by=S001
创建白板会话失败: FOREIGN KEY constraint failed
客户端已断开: Bk9CG8aJcS0uojrwAAAD
清理白板用户状态失败: database is locked
客户端已连接: aJegX4LEza6j-Pm_AAAH 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
验证白板会话参数: group_id=412462e9-b42f-47ca-acd8-3b0200ce0ee3, course_schedule_id=CS_001, created_by=S001
创建白板会话失败: FOREIGN KEY constraint failed
客户端已断开: aJegX4LEza6j-Pm_AAAH
清理白板用户状态失败: database is locked
客户端已连接: roq188WCdKlndSn2AAAL 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
验证白板会话参数: group_id=412462e9-b42f-47ca-acd8-3b0200ce0ee3, course_schedule_id=CS_001, created_by=S001
创建白板会话失败: database is locked
客户端已断开: roq188WCdKlndSn2AAAL
清理白板用户状态失败: database is locked
客户端已连接: Tod6DX6gV9B0Sk1PAAAP 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
验证白板会话参数: group_id=412462e9-b42f-47ca-acd8-3b0200ce0ee3, course_schedule_id=CS_001, created_by=S001
创建白板会话失败: FOREIGN KEY constraint failed
客户端已断开: Tod6DX6gV9B0Sk1PAAAP
清理白板用户状态失败: database is locked
客户端已连接: 6JRxHh2ONKU4QhZRAAAS 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
客户端已连接: rdJprdTrapwmX-f1AAAU 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
验证白板会话参数: group_id=412462e9-b42f-47ca-acd8-3b0200ce0ee3, course_schedule_id=CS_001, created_by=S001
创建白板会话失败: database is locked
客户端已断开: 6JRxHh2ONKU4QhZRAAAS
客户端已连接: Rwje-3YQzkYBxqPNAAAB 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
开始执行白板清理任务 - 2025-07-18 18:54:28.906349
清理不活跃会话失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个不活跃的白板会话
清理离线用户失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个离线用户记录
清理操作记录失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 条过期操作记录
清理内容版本失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个过期内容版本
数据库优化失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
白板清理任务完成 - 2025-07-18 18:54:28.906421
白板清理任务已启动，清理间隔: 1 小时
客户端已连接: nseDIX3cdjl1cuh7AAAB 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
开始执行白板清理任务 - 2025-07-18 18:55:34.022618
清理不活跃会话失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个不活跃的白板会话
清理离线用户失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个离线用户记录
清理操作记录失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 条过期操作记录
清理内容版本失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个过期内容版本
数据库优化失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
白板清理任务完成 - 2025-07-18 18:55:34.022690
白板清理任务已启动，清理间隔: 1 小时
客户端已连接: wr-xltauir4IYRvLAAAB 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
开始执行白板清理任务 - 2025-07-18 18:58:14.528086
清理不活跃会话失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个不活跃的白板会话
清理离线用户失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个离线用户记录
清理操作记录失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 条过期操作记录
清理内容版本失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个过期内容版本
数据库优化失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
白板清理任务完成 - 2025-07-18 18:58:14.528176
白板清理任务已启动，清理间隔: 1 小时
客户端已连接: qqGMYkU6iO2ZNsEeAAAB 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
客户端已断开: qqGMYkU6iO2ZNsEeAAAB
客户端已连接: oCkoQL1zlunlgwClAAAD 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
学生 张三 尝试加入白板: group_id=412462e9-b42f-47ca-acd8-3b0200ce0ee3, course_schedule_id=CS_001
未找到现有会话，创建新会话...
验证白板会话参数: group_id=412462e9-b42f-47ca-acd8-3b0200ce0ee3, course_schedule_id=CS_001, created_by=S001
所有外键验证通过，开始创建会话...
创建白板会话失败: FOREIGN KEY constraint failed
创建白板会话失败
开始执行白板清理任务 - 2025-07-18 18:58:26.526811
清理不活跃会话失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个不活跃的白板会话
清理离线用户失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个离线用户记录
清理操作记录失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 条过期操作记录
清理内容版本失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个过期内容版本
数据库优化失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
白板清理任务完成 - 2025-07-18 18:58:26.526877
白板清理任务已启动，清理间隔: 1 小时
客户端已断开: oCkoQL1zlunlgwClAAAD
清理白板用户状态失败: database is locked
客户端已连接: THYxwhnMpwGcXtZgAAAH 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
学生 张三 尝试加入白板: group_id=412462e9-b42f-47ca-acd8-3b0200ce0ee3, course_schedule_id=CS_001
未找到现有会话，创建新会话...
验证白板会话参数: group_id=412462e9-b42f-47ca-acd8-3b0200ce0ee3, course_schedule_id=CS_001, created_by=S001
所有外键验证通过，开始创建会话...
创建白板会话失败: FOREIGN KEY constraint failed
创建白板会话失败
客户端已连接: wSO_ShsGK6niNWI5AAAB 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
开始执行白板清理任务 - 2025-07-18 18:59:38.289198
清理不活跃会话失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个不活跃的白板会话
清理离线用户失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个离线用户记录
清理操作记录失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 条过期操作记录
清理内容版本失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个过期内容版本
数据库优化失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
白板清理任务完成 - 2025-07-18 18:59:38.289297
白板清理任务已启动，清理间隔: 1 小时
客户端已连接: DP2XIgcvDnHe2bobAAAB 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
开始执行白板清理任务 - 2025-07-18 18:59:55.323431
清理不活跃会话失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个不活跃的白板会话
清理离线用户失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个离线用户记录
清理操作记录失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 条过期操作记录
清理内容版本失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个过期内容版本
数据库优化失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
白板清理任务完成 - 2025-07-18 18:59:55.323502
白板清理任务已启动，清理间隔: 1 小时
客户端已连接: RDK7U9cS87Tck9jFAAAB 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
开始执行白板清理任务 - 2025-07-18 19:00:09.569738
清理不活跃会话失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个不活跃的白板会话
清理离线用户失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个离线用户记录
清理操作记录失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 条过期操作记录
清理内容版本失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个过期内容版本
数据库优化失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
白板清理任务完成 - 2025-07-18 19:00:09.569847
白板清理任务已启动，清理间隔: 1 小时
客户端已连接: AG5s9lTKrJryupmtAAAB 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
开始执行白板清理任务 - 2025-07-18 19:00:23.677210
清理不活跃会话失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个不活跃的白板会话
清理离线用户失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个离线用户记录
清理操作记录失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 条过期操作记录
清理内容版本失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个过期内容版本
数据库优化失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
白板清理任务完成 - 2025-07-18 19:00:23.677287
白板清理任务已启动，清理间隔: 1 小时
客户端已连接: i2wcfeGnuGkqQGnsAAAB 来自IP: ************
  User-Agent: Mozilla/5.0 (X11; Linux x86_64; rv:140.0) Gecko/20100101 Firefox/140.0
开始执行白板清理任务 - 2025-07-18 19:00:35.645455
清理不活跃会话失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个不活跃的白板会话
清理离线用户失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个离线用户记录
清理操作记录失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 条过期操作记录
清理内容版本失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个过期内容版本
数据库优化失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
白板清理任务完成 - 2025-07-18 19:00:35.645572
白板清理任务已启动，清理间隔: 1 小时
开始执行白板清理任务 - 2025-07-18 18:39:37.734104
清理不活跃会话失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个不活跃的白板会话
清理离线用户失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个离线用户记录
清理操作记录失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 条过期操作记录
清理内容版本失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
清理了 0 个过期内容版本
数据库优化失败: Working outside of application context.

This typically means that you attempted to use functionality that needed
the current application. To solve this, set up an application context
with app.app_context(). See the documentation for more information.
白板清理任务完成 - 2025-07-18 18:39:37.734196
白板清理任务已启动，清理间隔: 1 小时
