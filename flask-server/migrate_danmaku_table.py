#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
弹幕表结构迁移脚本
将 student_id 列迁移为 user_id，并添加 user_type 列
"""

import sqlite3
import os
import sys
from datetime import datetime

def migrate_danmaku_table(db_path):
    """迁移弹幕表结构"""
    print(f"开始迁移数据库: {db_path}")
    
    # 备份数据库
    backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"数据库已备份到: {backup_path}")
    except Exception as e:
        print(f"备份失败: {e}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查当前表结构
        cursor.execute("PRAGMA table_info(danmaku)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"当前表结构: {column_names}")
        
        # 检查是否需要迁移
        if 'user_id' in column_names and 'user_type' in column_names:
            print("表结构已经是最新的，无需迁移")
            conn.close()
            return True
        
        if 'student_id' not in column_names:
            print("未找到 student_id 列，可能表结构已经更新")
            conn.close()
            return True
        
        print("开始迁移表结构...")
        
        # 1. 创建新的临时表
        cursor.execute('''
            CREATE TABLE danmaku_new (
                id TEXT PRIMARY KEY,
                course_schedule_id TEXT NOT NULL,
                user_id TEXT NOT NULL,
                user_type TEXT NOT NULL DEFAULT 'student',
                messages TEXT NOT NULL,
                message_count INTEGER DEFAULT 0,
                first_message_time TEXT,
                last_message_time TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                UNIQUE(course_schedule_id, user_id),
                FOREIGN KEY (course_schedule_id) REFERENCES course_schedules(id)
            )
        ''')
        
        # 2. 复制数据到新表
        cursor.execute('''
            INSERT INTO danmaku_new 
            (id, course_schedule_id, user_id, user_type, messages, message_count, 
             first_message_time, last_message_time, created_at, updated_at)
            SELECT 
                id, course_schedule_id, student_id, 'student', messages, message_count,
                first_message_time, last_message_time, created_at, updated_at
            FROM danmaku
        ''')
        
        # 3. 删除旧表
        cursor.execute('DROP TABLE danmaku')
        
        # 4. 重命名新表
        cursor.execute('ALTER TABLE danmaku_new RENAME TO danmaku')
        
        # 5. 提交更改
        conn.commit()
        
        print("表结构迁移完成")
        
        # 验证迁移结果
        cursor.execute("SELECT COUNT(*) FROM danmaku")
        count = cursor.fetchone()[0]
        print(f"迁移后记录数: {count}")
        
        cursor.execute("PRAGMA table_info(danmaku)")
        new_columns = cursor.fetchall()
        new_column_names = [col[1] for col in new_columns]
        print(f"新表结构: {new_column_names}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"迁移失败: {e}")
        try:
            conn.rollback()
            conn.close()
        except:
            pass
        return False

def main():
    """主函数"""
    # 默认数据库路径
    default_db_path = "instance/database.db"
    
    # 从命令行参数获取数据库路径
    if len(sys.argv) > 1:
        db_path = sys.argv[1]
    else:
        db_path = default_db_path
    
    # 检查数据库文件是否存在
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        print("请确认数据库路径是否正确")
        return False
    
    # 执行迁移
    success = migrate_danmaku_table(db_path)
    
    if success:
        print("✅ 数据库迁移成功完成！")
        print("\n注意事项：")
        print("1. 数据库已备份，如有问题可以恢复")
        print("2. 请重启应用程序以使更改生效")
        print("3. 建议测试所有相关功能确保正常工作")
    else:
        print("❌ 数据库迁移失败！")
        print("请检查错误信息并手动修复")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
