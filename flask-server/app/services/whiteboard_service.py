# flask-server/app/services/whiteboard_service.py
import json
import uuid
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
from app.models.database import get_db


class WhiteboardService:
    """白板协同服务类"""
    
    def __init__(self):
        self.active_sessions = {}  # 内存中的活跃会话缓存
        self.operation_sequence = {}  # 操作序列号管理
    
    def create_session(self, group_id: str, course_schedule_id: str, 
                      session_name: str, created_by: str) -> str:
        """创建白板会话"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            session_id = str(uuid.uuid4())
            now = datetime.now().isoformat()
            
            cursor.execute("""
                INSERT INTO whiteboard_sessions 
                (id, group_id, course_schedule_id, session_name, created_by, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (session_id, group_id, course_schedule_id, session_name, created_by, now, now))
            
            # 创建初始空白内容
            content_id = str(uuid.uuid4())
            initial_content = {
                "elements": [],
                "appState": {
                    "viewBackgroundColor": "#ffffff",
                    "currentItemStrokeColor": "#000000",
                    "currentItemBackgroundColor": "transparent"
                }
            }
            
            cursor.execute("""
                INSERT INTO whiteboard_contents 
                (id, session_id, content_data, app_state, created_at, is_snapshot)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (content_id, session_id, json.dumps(initial_content["elements"]), 
                  json.dumps(initial_content["appState"]), now, True))
            
            conn.commit()
            conn.close()
            
            # 初始化内存状态
            self.active_sessions[session_id] = {
                "group_id": group_id,
                "course_schedule_id": course_schedule_id,
                "participants": {},
                "last_content": initial_content,
                "collaboration_enabled": True
            }
            self.operation_sequence[session_id] = 0
            
            return session_id
            
        except Exception as e:
            print(f"创建白板会话失败: {e}")
            return None
    
    def join_session(self, session_id: str, user_id: str, user_type: str, 
                    user_name: str, role: str = "editor") -> bool:
        """用户加入白板会话"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            # 检查会话是否存在
            cursor.execute("SELECT * FROM whiteboard_sessions WHERE id = ?", (session_id,))
            session = cursor.fetchone()
            if not session:
                return False
            
            now = datetime.now().isoformat()
            participant_id = str(uuid.uuid4())
            
            # 插入或更新参与者记录
            cursor.execute("""
                INSERT OR REPLACE INTO whiteboard_participants 
                (id, session_id, user_id, user_type, user_name, role, joined_at, last_active_at, is_online)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (participant_id, session_id, user_id, user_type, user_name, role, now, now, True))
            
            conn.commit()
            conn.close()
            
            # 更新内存状态
            if session_id not in self.active_sessions:
                self.active_sessions[session_id] = {
                    "group_id": session["group_id"],
                    "course_schedule_id": session["course_schedule_id"],
                    "participants": {},
                    "last_content": None,
                    "collaboration_enabled": session["collaboration_enabled"]
                }
            
            self.active_sessions[session_id]["participants"][user_id] = {
                "user_type": user_type,
                "user_name": user_name,
                "role": role,
                "last_active": time.time()
            }
            
            return True
            
        except Exception as e:
            print(f"加入白板会话失败: {e}")
            return False
    
    def leave_session(self, session_id: str, user_id: str) -> bool:
        """用户离开白板会话"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            now = datetime.now().isoformat()
            cursor.execute("""
                UPDATE whiteboard_participants 
                SET is_online = 0, left_at = ?
                WHERE session_id = ? AND user_id = ?
            """, (now, session_id, user_id))
            
            conn.commit()
            conn.close()
            
            # 更新内存状态
            if session_id in self.active_sessions:
                self.active_sessions[session_id]["participants"].pop(user_id, None)
            
            return True
            
        except Exception as e:
            print(f"离开白板会话失败: {e}")
            return False
    
    def record_operation(self, session_id: str, user_id: str, user_type: str,
                        operation_type: str, operation_data: Dict) -> bool:
        """记录白板操作"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            # 获取下一个序列号
            if session_id not in self.operation_sequence:
                self.operation_sequence[session_id] = 0
            self.operation_sequence[session_id] += 1
            
            operation_id = str(uuid.uuid4())
            now = datetime.now().isoformat()
            timestamp = int(time.time() * 1000)  # 毫秒时间戳
            
            cursor.execute("""
                INSERT INTO whiteboard_operations 
                (id, session_id, user_id, user_type, operation_type, operation_data, 
                 timestamp, sequence_number, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (operation_id, session_id, user_id, user_type, operation_type,
                  json.dumps(operation_data), timestamp, 
                  self.operation_sequence[session_id], now))
            
            conn.commit()
            conn.close()
            
            return True
            
        except Exception as e:
            print(f"记录白板操作失败: {e}")
            return False
    
    def save_content(self, session_id: str, elements: List, app_state: Dict, 
                    created_by: str = None, is_snapshot: bool = False) -> bool:
        """保存白板内容"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            # 获取当前版本号
            cursor.execute("""
                SELECT MAX(version) as max_version FROM whiteboard_contents 
                WHERE session_id = ?
            """, (session_id,))
            result = cursor.fetchone()
            version = (result["max_version"] or 0) + 1
            
            content_id = str(uuid.uuid4())
            now = datetime.now().isoformat()
            
            cursor.execute("""
                INSERT INTO whiteboard_contents 
                (id, session_id, content_data, app_state, version, created_by, created_at, is_snapshot)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (content_id, session_id, json.dumps(elements), json.dumps(app_state),
                  version, created_by, now, is_snapshot))
            
            conn.commit()
            conn.close()
            
            # 更新内存缓存
            if session_id in self.active_sessions:
                self.active_sessions[session_id]["last_content"] = {
                    "elements": elements,
                    "appState": app_state
                }
            
            return True
            
        except Exception as e:
            print(f"保存白板内容失败: {e}")
            return False
    
    def get_session_content(self, session_id: str) -> Optional[Dict]:
        """获取会话的最新内容"""
        try:
            # 先检查内存缓存
            if session_id in self.active_sessions and self.active_sessions[session_id]["last_content"]:
                return self.active_sessions[session_id]["last_content"]
            
            # 从数据库获取
            conn = get_db()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT content_data, app_state FROM whiteboard_contents 
                WHERE session_id = ? 
                ORDER BY version DESC 
                LIMIT 1
            """, (session_id,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                content = {
                    "elements": json.loads(result["content_data"]),
                    "appState": json.loads(result["app_state"]) if result["app_state"] else {}
                }
                
                # 更新内存缓存
                if session_id in self.active_sessions:
                    self.active_sessions[session_id]["last_content"] = content
                
                return content
            
            return None
            
        except Exception as e:
            print(f"获取白板内容失败: {e}")
            return None
    
    def get_session_participants(self, session_id: str) -> List[Dict]:
        """获取会话参与者列表"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT user_id, user_type, user_name, role, joined_at, last_active_at, is_online
                FROM whiteboard_participants 
                WHERE session_id = ? AND is_online = 1
                ORDER BY joined_at
            """, (session_id,))
            
            participants = []
            for row in cursor.fetchall():
                participants.append({
                    "user_id": row["user_id"],
                    "user_type": row["user_type"],
                    "user_name": row["user_name"],
                    "role": row["role"],
                    "joined_at": row["joined_at"],
                    "last_active_at": row["last_active_at"],
                    "is_online": bool(row["is_online"])
                })
            
            conn.close()
            return participants
            
        except Exception as e:
            print(f"获取参与者列表失败: {e}")
            return []
    
    def update_collaboration_status(self, session_id: str, enabled: bool) -> bool:
        """更新协作状态"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            now = datetime.now().isoformat()
            cursor.execute("""
                UPDATE whiteboard_sessions 
                SET collaboration_enabled = ?, updated_at = ?
                WHERE id = ?
            """, (enabled, now, session_id))
            
            conn.commit()
            conn.close()
            
            # 更新内存状态
            if session_id in self.active_sessions:
                self.active_sessions[session_id]["collaboration_enabled"] = enabled
            
            return True
            
        except Exception as e:
            print(f"更新协作状态失败: {e}")
            return False
    
    def get_session_by_group(self, group_id: str, course_schedule_id: str) -> Optional[str]:
        """根据分组ID和课程ID获取活跃的白板会话"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id FROM whiteboard_sessions 
                WHERE group_id = ? AND course_schedule_id = ? AND status = 'active'
                ORDER BY created_at DESC 
                LIMIT 1
            """, (group_id, course_schedule_id))
            
            result = cursor.fetchone()
            conn.close()
            
            return result["id"] if result else None
            
        except Exception as e:
            print(f"获取分组会话失败: {e}")
            return None


# 全局服务实例
whiteboard_service = WhiteboardService()
