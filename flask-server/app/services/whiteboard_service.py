# flask-server/app/services/whiteboard_service.py
import json
import uuid
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from app.models.database import get_db


class WhiteboardService:
    """白板协同服务类"""

    def __init__(self):
        self.active_sessions = {}  # 内存中的活跃会话缓存
        self.operation_sequence = {}  # 操作序列号管理
        self.content_cache = {}  # 内容缓存，减少数据库查询
        self.cache_timeout = 300  # 缓存超时时间（秒）
        self.max_operations_per_session = 1000  # 每个会话最大操作记录数
    
    def create_session(self, group_id: str, course_schedule_id: str,
                      session_name: str, created_by: str) -> str:
        """创建白板会话"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证外键约束
            print(f"验证白板会话参数: group_id={group_id}, course_schedule_id={course_schedule_id}, created_by={created_by}")

            cursor.execute("SELECT id FROM class_groups WHERE id = ?", (group_id,))
            group_result = cursor.fetchone()
            if not group_result:
                print(f"分组不存在: {group_id}")
                # 列出所有可用分组
                cursor.execute("SELECT id, group_name FROM class_groups LIMIT 5")
                available_groups = cursor.fetchall()
                print(f"可用分组: {available_groups}")
                return None

            cursor.execute("SELECT id FROM course_schedules WHERE id = ?", (course_schedule_id,))
            course_result = cursor.fetchone()
            if not course_result:
                print(f"课程安排不存在: {course_schedule_id}")
                # 列出所有可用课程安排
                cursor.execute("SELECT id FROM course_schedules LIMIT 5")
                available_courses = cursor.fetchall()
                print(f"可用课程安排: {available_courses}")
                return None

            # 检查创建者是否存在（可以是教师或学生）
            cursor.execute("SELECT teacher_id FROM teachers WHERE teacher_id = ?", (created_by,))
            teacher_result = cursor.fetchone()
            cursor.execute("SELECT student_id FROM students WHERE student_id = ?", (created_by,))
            student_result = cursor.fetchone()

            if not teacher_result and not student_result:
                print(f"创建者不存在: {created_by}")
                # 列出所有可用教师和学生
                cursor.execute("SELECT teacher_id, name FROM teachers LIMIT 3")
                available_teachers = cursor.fetchall()
                cursor.execute("SELECT student_id, name FROM students LIMIT 3")
                available_students = cursor.fetchall()
                print(f"可用教师: {available_teachers}")
                print(f"可用学生: {available_students}")
                return None

            # 验证分组和课程安排的关联
            cursor.execute("SELECT course_schedule_id FROM class_groups WHERE id = ?", (group_id,))
            group_course = cursor.fetchone()
            if group_course and group_course['course_schedule_id'] != course_schedule_id:
                print(f"分组 {group_id} 关联的课程安排是 {group_course['course_schedule_id']}，不是 {course_schedule_id}")
                return None

            print(f"所有外键验证通过，开始创建会话...")

            session_id = str(uuid.uuid4())
            now = datetime.now().isoformat()
            
            # 使用重试机制处理数据库锁定
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    cursor.execute("""
                        INSERT INTO whiteboard_sessions
                        (id, group_id, course_schedule_id, session_name, created_by, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (session_id, group_id, course_schedule_id, session_name, created_by, now, now))
                    break
                except Exception as e:
                    if "database is locked" in str(e) and attempt < max_retries - 1:
                        print(f"数据库锁定，重试 {attempt + 1}/{max_retries}")
                        time.sleep(0.1 * (attempt + 1))  # 递增延迟
                        continue
                    else:
                        raise e
            
            # 创建初始空白内容
            content_id = str(uuid.uuid4())
            initial_content = {
                "elements": [],
                "appState": {
                    "viewBackgroundColor": "#ffffff",
                    "currentItemStrokeColor": "#000000",
                    "currentItemBackgroundColor": "transparent"
                }
            }
            
            cursor.execute("""
                INSERT INTO whiteboard_contents 
                (id, session_id, content_data, app_state, created_at, is_snapshot)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (content_id, session_id, json.dumps(initial_content["elements"]), 
                  json.dumps(initial_content["appState"]), now, True))
            
            conn.commit()
            conn.close()
            
            # 初始化内存状态
            self.active_sessions[session_id] = {
                "group_id": group_id,
                "course_schedule_id": course_schedule_id,
                "participants": {},
                "last_content": initial_content,
                "collaboration_enabled": True
            }
            self.operation_sequence[session_id] = 0
            
            return session_id
            
        except Exception as e:
            print(f"创建白板会话失败: {e}")
            return None
    
    def join_session(self, session_id: str, user_id: str, user_type: str, 
                    user_name: str, role: str = "editor") -> bool:
        """用户加入白板会话"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            # 检查会话是否存在
            cursor.execute("SELECT * FROM whiteboard_sessions WHERE id = ?", (session_id,))
            session = cursor.fetchone()
            if not session:
                return False
            
            now = datetime.now().isoformat()
            participant_id = str(uuid.uuid4())
            
            # 插入或更新参与者记录
            cursor.execute("""
                INSERT OR REPLACE INTO whiteboard_participants 
                (id, session_id, user_id, user_type, user_name, role, joined_at, last_active_at, is_online)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (participant_id, session_id, user_id, user_type, user_name, role, now, now, True))
            
            conn.commit()
            conn.close()
            
            # 更新内存状态
            if session_id not in self.active_sessions:
                self.active_sessions[session_id] = {
                    "group_id": session["group_id"],
                    "course_schedule_id": session["course_schedule_id"],
                    "participants": {},
                    "last_content": None,
                    "collaboration_enabled": session["collaboration_enabled"]
                }
            
            self.active_sessions[session_id]["participants"][user_id] = {
                "user_type": user_type,
                "user_name": user_name,
                "role": role,
                "last_active": time.time()
            }
            
            return True
            
        except Exception as e:
            print(f"加入白板会话失败: {e}")
            return False
    
    def leave_session(self, session_id: str, user_id: str) -> bool:
        """用户离开白板会话"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            now = datetime.now().isoformat()
            cursor.execute("""
                UPDATE whiteboard_participants 
                SET is_online = 0, left_at = ?
                WHERE session_id = ? AND user_id = ?
            """, (now, session_id, user_id))
            
            conn.commit()
            conn.close()
            
            # 更新内存状态
            if session_id in self.active_sessions:
                self.active_sessions[session_id]["participants"].pop(user_id, None)
            
            return True
            
        except Exception as e:
            print(f"离开白板会话失败: {e}")
            return False
    
    def record_operation(self, session_id: str, user_id: str, user_type: str,
                        operation_type: str, operation_data: Dict) -> bool:
        """记录白板操作"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 获取下一个序列号
            if session_id not in self.operation_sequence:
                # 从数据库获取当前最大序列号
                cursor.execute("""
                    SELECT MAX(sequence_number) as max_seq FROM whiteboard_operations
                    WHERE session_id = ?
                """, (session_id,))
                result = cursor.fetchone()
                self.operation_sequence[session_id] = (result["max_seq"] or 0)

            self.operation_sequence[session_id] += 1

            operation_id = str(uuid.uuid4())
            now = datetime.now().isoformat()
            timestamp = int(time.time() * 1000)  # 毫秒时间戳

            cursor.execute("""
                INSERT INTO whiteboard_operations
                (id, session_id, user_id, user_type, operation_type, operation_data,
                 timestamp, sequence_number, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (operation_id, session_id, user_id, user_type, operation_type,
                  json.dumps(operation_data), timestamp,
                  self.operation_sequence[session_id], now))

            # 清理旧操作记录（保留最近的操作）
            if self.operation_sequence[session_id] % 100 == 0:  # 每100个操作清理一次
                cursor.execute("""
                    DELETE FROM whiteboard_operations
                    WHERE session_id = ? AND sequence_number < ?
                """, (session_id, self.operation_sequence[session_id] - self.max_operations_per_session))

            conn.commit()
            conn.close()

            return True

        except Exception as e:
            print(f"记录白板操作失败: {e}")
            return False
    
    def save_content(self, session_id: str, elements: List, app_state: Dict,
                    created_by: str = None, is_snapshot: bool = False) -> bool:
        """保存白板内容"""
        try:
            # 内容验证
            if not self._validate_content(elements, app_state):
                print(f"白板内容验证失败: session_id={session_id}")
                return False

            conn = get_db()
            cursor = conn.cursor()

            # 获取当前版本号
            cursor.execute("""
                SELECT MAX(version) as max_version FROM whiteboard_contents
                WHERE session_id = ?
            """, (session_id,))
            result = cursor.fetchone()
            version = (result["max_version"] or 0) + 1

            content_id = str(uuid.uuid4())
            now = datetime.now().isoformat()

            # 压缩大型内容
            elements_json = self._compress_content(json.dumps(elements))
            app_state_json = json.dumps(app_state)

            cursor.execute("""
                INSERT INTO whiteboard_contents
                (id, session_id, content_data, app_state, version, created_by, created_at, is_snapshot)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, (content_id, session_id, elements_json, app_state_json,
                  version, created_by, now, is_snapshot))

            # 清理旧版本（保留最近20个版本）
            if not is_snapshot:
                cursor.execute("""
                    DELETE FROM whiteboard_contents
                    WHERE session_id = ? AND is_snapshot = 0 AND version < ?
                """, (session_id, version - 20))

            conn.commit()
            conn.close()

            # 更新内存缓存
            cache_key = f"content_{session_id}"
            self.content_cache[cache_key] = {
                "elements": elements,
                "appState": app_state,
                "timestamp": time.time()
            }

            if session_id in self.active_sessions:
                self.active_sessions[session_id]["last_content"] = {
                    "elements": elements,
                    "appState": app_state
                }

            return True

        except Exception as e:
            print(f"保存白板内容失败: {e}")
            return False

    def _validate_content(self, elements: List, app_state: Dict) -> bool:
        """验证白板内容"""
        try:
            # 检查元素数量限制
            if len(elements) > 10000:  # 限制最大元素数量
                return False

            # 检查单个元素大小
            for element in elements:
                if not isinstance(element, dict):
                    return False
                # 检查必要字段
                if 'id' not in element or 'type' not in element:
                    return False

            # 检查appState
            if not isinstance(app_state, dict):
                return False

            return True

        except Exception as e:
            print(f"内容验证失败: {e}")
            return False

    def _compress_content(self, content_str: str) -> str:
        """压缩内容（如果需要）"""
        try:
            # 如果内容超过一定大小，可以考虑压缩
            if len(content_str) > 100000:  # 100KB
                import gzip
                import base64
                compressed = gzip.compress(content_str.encode('utf-8'))
                return base64.b64encode(compressed).decode('utf-8')
            return content_str
        except Exception:
            return content_str
    
    def get_session_content(self, session_id: str) -> Optional[Dict]:
        """获取会话的最新内容"""
        try:
            # 检查内容缓存
            cache_key = f"content_{session_id}"
            if cache_key in self.content_cache:
                cached_content = self.content_cache[cache_key]
                # 检查缓存是否过期
                if time.time() - cached_content["timestamp"] < self.cache_timeout:
                    return {
                        "elements": cached_content["elements"],
                        "appState": cached_content["appState"]
                    }
                else:
                    # 清理过期缓存
                    del self.content_cache[cache_key]

            # 先检查内存会话缓存
            if session_id in self.active_sessions and self.active_sessions[session_id]["last_content"]:
                return self.active_sessions[session_id]["last_content"]

            # 从数据库获取
            conn = get_db()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT content_data, app_state FROM whiteboard_contents
                WHERE session_id = ?
                ORDER BY version DESC
                LIMIT 1
            """, (session_id,))

            result = cursor.fetchone()
            conn.close()

            if result:
                # 处理可能的压缩内容
                content_data = self._decompress_content(result["content_data"])

                content = {
                    "elements": json.loads(content_data),
                    "appState": json.loads(result["app_state"]) if result["app_state"] else {}
                }

                # 更新缓存
                self.content_cache[cache_key] = {
                    "elements": content["elements"],
                    "appState": content["appState"],
                    "timestamp": time.time()
                }

                # 更新内存缓存
                if session_id in self.active_sessions:
                    self.active_sessions[session_id]["last_content"] = content

                return content

            return None

        except Exception as e:
            print(f"获取白板内容失败: {e}")
            return None

    def _decompress_content(self, content_str: str) -> str:
        """解压缩内容"""
        try:
            # 尝试检测是否为压缩内容
            if len(content_str) > 0 and content_str[0] not in '[{':
                import gzip
                import base64
                compressed_data = base64.b64decode(content_str.encode('utf-8'))
                return gzip.decompress(compressed_data).decode('utf-8')
            return content_str
        except Exception:
            return content_str
    
    def get_session_participants(self, session_id: str) -> List[Dict]:
        """获取会话参与者列表"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT user_id, user_type, user_name, role, joined_at, last_active_at, is_online
                FROM whiteboard_participants 
                WHERE session_id = ? AND is_online = 1
                ORDER BY joined_at
            """, (session_id,))
            
            participants = []
            for row in cursor.fetchall():
                participants.append({
                    "user_id": row["user_id"],
                    "user_type": row["user_type"],
                    "user_name": row["user_name"],
                    "role": row["role"],
                    "joined_at": row["joined_at"],
                    "last_active_at": row["last_active_at"],
                    "is_online": bool(row["is_online"])
                })
            
            conn.close()
            return participants
            
        except Exception as e:
            print(f"获取参与者列表失败: {e}")
            return []
    
    def update_collaboration_status(self, session_id: str, enabled: bool) -> bool:
        """更新协作状态"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            now = datetime.now().isoformat()
            cursor.execute("""
                UPDATE whiteboard_sessions 
                SET collaboration_enabled = ?, updated_at = ?
                WHERE id = ?
            """, (enabled, now, session_id))
            
            conn.commit()
            conn.close()
            
            # 更新内存状态
            if session_id in self.active_sessions:
                self.active_sessions[session_id]["collaboration_enabled"] = enabled
            
            return True
            
        except Exception as e:
            print(f"更新协作状态失败: {e}")
            return False
    
    def get_session_by_group(self, group_id: str, course_schedule_id: str) -> Optional[str]:
        """根据分组ID和课程ID获取活跃的白板会话"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id FROM whiteboard_sessions 
                WHERE group_id = ? AND course_schedule_id = ? AND status = 'active'
                ORDER BY created_at DESC 
                LIMIT 1
            """, (group_id, course_schedule_id))
            
            result = cursor.fetchone()
            conn.close()
            
            return result["id"] if result else None
            
        except Exception as e:
            print(f"获取分组会话失败: {e}")
            return None

    def cleanup_inactive_sessions(self, hours: int = 24) -> int:
        """清理不活跃的会话"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 标记长时间无活动的会话为结束
            cutoff_time = datetime.now() - timedelta(hours=hours)
            cursor.execute("""
                UPDATE whiteboard_sessions
                SET status = 'ended', ended_at = ?
                WHERE status = 'active' AND updated_at < ?
            """, (datetime.now().isoformat(), cutoff_time.isoformat()))

            cleaned_count = cursor.rowcount

            # 清理内存缓存
            sessions_to_remove = []
            for session_id in self.active_sessions:
                if session_id not in [s['id'] for s in self._get_active_sessions_from_db()]:
                    sessions_to_remove.append(session_id)

            for session_id in sessions_to_remove:
                del self.active_sessions[session_id]
                if session_id in self.operation_sequence:
                    del self.operation_sequence[session_id]

            # 清理内容缓存
            current_time = time.time()
            cache_keys_to_remove = []
            for cache_key, cache_data in self.content_cache.items():
                if current_time - cache_data["timestamp"] > self.cache_timeout:
                    cache_keys_to_remove.append(cache_key)

            for cache_key in cache_keys_to_remove:
                del self.content_cache[cache_key]

            conn.commit()
            conn.close()

            print(f"清理了 {cleaned_count} 个不活跃的白板会话")
            return cleaned_count

        except Exception as e:
            print(f"清理不活跃会话失败: {e}")
            return 0

    def _get_active_sessions_from_db(self) -> List[Dict]:
        """从数据库获取活跃会话列表"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, group_id, course_schedule_id, collaboration_enabled
                FROM whiteboard_sessions
                WHERE status = 'active'
            """)

            sessions = []
            for row in cursor.fetchall():
                sessions.append({
                    'id': row['id'],
                    'group_id': row['group_id'],
                    'course_schedule_id': row['course_schedule_id'],
                    'collaboration_enabled': bool(row['collaboration_enabled'])
                })

            conn.close()
            return sessions

        except Exception as e:
            print(f"获取活跃会话失败: {e}")
            return []

    def get_session_statistics(self, session_id: str) -> Dict:
        """获取会话统计信息"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 获取基本信息
            cursor.execute("""
                SELECT ws.*, cg.group_name, c.name as course_name
                FROM whiteboard_sessions ws
                JOIN class_groups cg ON ws.group_id = cg.id
                JOIN course_schedules cs ON ws.course_schedule_id = cs.id
                JOIN courses c ON cs.course_id = c.id
                WHERE ws.id = ?
            """, (session_id,))

            session_info = cursor.fetchone()
            if not session_info:
                return {}

            # 获取参与者统计
            cursor.execute("""
                SELECT COUNT(*) as total_participants,
                       COUNT(CASE WHEN is_online = 1 THEN 1 END) as online_participants
                FROM whiteboard_participants
                WHERE session_id = ?
            """, (session_id,))
            participant_stats = cursor.fetchone()

            # 获取操作统计
            cursor.execute("""
                SELECT COUNT(*) as total_operations,
                       COUNT(DISTINCT user_id) as active_users
                FROM whiteboard_operations
                WHERE session_id = ?
            """, (session_id,))
            operation_stats = cursor.fetchone()

            # 获取内容版本数
            cursor.execute("""
                SELECT COUNT(*) as total_versions,
                       COUNT(CASE WHEN is_snapshot = 1 THEN 1 END) as snapshots
                FROM whiteboard_contents
                WHERE session_id = ?
            """, (session_id,))
            content_stats = cursor.fetchone()

            conn.close()

            return {
                'session_info': {
                    'session_id': session_info['id'],
                    'group_name': session_info['group_name'],
                    'course_name': session_info['course_name'],
                    'created_at': session_info['created_at'],
                    'updated_at': session_info['updated_at'],
                    'collaboration_enabled': bool(session_info['collaboration_enabled'])
                },
                'participants': {
                    'total': participant_stats['total_participants'],
                    'online': participant_stats['online_participants']
                },
                'operations': {
                    'total': operation_stats['total_operations'],
                    'active_users': operation_stats['active_users']
                },
                'content': {
                    'versions': content_stats['total_versions'],
                    'snapshots': content_stats['snapshots']
                }
            }

        except Exception as e:
            print(f"获取会话统计失败: {e}")
            return {}


# 全局服务实例
whiteboard_service = WhiteboardService()
