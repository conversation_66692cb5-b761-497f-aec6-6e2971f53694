# flask-server/app/socket_events.py
from flask import session, request, current_app
from flask_socketio import emit, join_room, leave_room
from app import socketio, active_timers
from app.models.database import get_db
# 使用简单的token生成替代jose
import hashlib
import json
import base64
import time

class SimpleJWT:
    @staticmethod
    def encode(payload, secret, algorithm='HS256'):
        # 简化的JWT实现
        header = {'typ': 'JWT', 'alg': algorithm}

        # 确保secret是字符串类型
        if isinstance(secret, bytes):
            secret = secret.decode('utf-8')
        elif not isinstance(secret, str):
            secret = str(secret)

        # 转换datetime为timestamp
        if 'exp' in payload and hasattr(payload['exp'], 'timestamp'):
            payload['exp'] = int(payload['exp'].timestamp())
        if 'iat' in payload and hasattr(payload['iat'], 'timestamp'):
            payload['iat'] = int(payload['iat'].timestamp())

        header_b64 = base64.urlsafe_b64encode(json.dumps(header).encode()).decode().rstrip('=')
        payload_b64 = base64.urlsafe_b64encode(json.dumps(payload).encode()).decode().rstrip('=')

        signature_input = f"{header_b64}.{payload_b64}"
        signature = hashlib.sha256((signature_input + secret).encode()).hexdigest()

        return f"{header_b64}.{payload_b64}.{signature}"

    @staticmethod
    def decode(token, secret, algorithms=['HS256']):
        try:
            # 确保secret是字符串类型
            if isinstance(secret, bytes):
                secret = secret.decode('utf-8')
            elif not isinstance(secret, str):
                secret = str(secret)

            parts = token.split('.')
            if len(parts) != 3:
                raise ValueError("Invalid token format")

            header_b64, payload_b64, signature = parts

            # 验证签名
            signature_input = f"{header_b64}.{payload_b64}"
            expected_signature = hashlib.sha256((signature_input + secret).encode()).hexdigest()

            if signature != expected_signature:
                raise ValueError("Invalid signature")

            # 解码payload
            payload_json = base64.urlsafe_b64decode(payload_b64 + '==').decode()
            payload = json.loads(payload_json)

            # 检查过期时间
            if 'exp' in payload and payload['exp'] < time.time():
                raise ValueError("Token expired")

            return payload
        except Exception as e:
            raise ValueError(f"Token decode error: {e}")

jwt = SimpleJWT()
JWTError = ValueError  # 使用ValueError替代JWTError
import time
import json
import uuid
from datetime import datetime

# --- 全局存储在线小组的客户端信息 ---
# {sid: {'ip': '...', 'hostname': '...'}}
group_clients = {}
# {group_id: sid}
group_id_to_sid = {}
# 存储每个sid加入了哪个房间，方便断开时通知 {sid: room_name}
sid_to_room = {}
whiteboard_sessions = {}
# 存储白板状态 {room: data}
whiteboard_states = {}

# 导入白板服务
from app.services.whiteboard_service import whiteboard_service

@socketio.on('connect')
def on_connect():
    """当客户端连接时调用。"""
    client_ip = request.environ.get('REMOTE_ADDR', 'Unknown')
    user_agent = request.environ.get('HTTP_USER_AGENT', 'Unknown')
    print(f"客户端已连接: {request.sid} 来自IP: {client_ip}")
    print(f"  User-Agent: {user_agent}")

@socketio.on('authenticate')
def on_authenticate(data):
    """处理客户端认证请求"""
    token = data.get('token')
    if not token:
        emit('authenticated', {'success': False, 'error': 'No token provided'})
        print(f"客户端 {request.sid} 认证失败：未提供token")
        return
    
    try:
        # 验证JWT token
        payload = jwt.decode(
            token,
            current_app.config['SECRET_KEY'],
            algorithms=['HS256']
        )
        user_id = payload.get('sub')
        user_type = payload.get('user_type')
        
        # 将用户信息存储到session中供后续使用
        session['user_id'] = user_id
        session['user_type'] = user_type
        session['authenticated'] = True
        
        emit('authenticated', {'success': True})
        print(f"客户端 {request.sid} 认证成功：{user_type} {user_id}")
        
    except JWTError as e:    
        emit('authenticated', {'success': False, 'error': 'Invalid token'})
        print(f"客户端 {request.sid} 认证失败：{str(e)}")

@socketio.on('disconnect')
def on_disconnect():
    """当客户端断开连接时调用。"""
    sid = request.sid
    # 清理 group_clients 和 group_id_to_sid
    if sid in group_clients:
        # 找到对应的 group_id 并删除
        group_id_to_remove = None
        for gid, a_sid in group_id_to_sid.items():
            if a_sid == sid:
                group_id_to_remove = gid
                break
        if group_id_to_remove:
            del group_id_to_sid[group_id_to_remove]
            print(f"小组ID {group_id_to_remove} 的映射已移除")
        
        removed_client = group_clients.pop(sid)
        print(f"小组客户端已断开: {removed_client.get('hostname')} ({removed_client.get('ip')})")
    else:
        print(f"客户端已断开: {sid}")

    # 清理 sid_to_room
    if sid in sid_to_room:
        room = sid_to_room.pop(sid)
        leave_room(room)
        print(f"客户端 {sid} 已从房间 {room} 离开")

    # 清理白板会话中的用户
    for group_id, users in whiteboard_sessions.items():
        whiteboard_sessions[group_id] = [u for u in users if u['sid'] != sid]

    # 处理白板协同用户断线
    try:
        # 更新白板参与者离线状态
        conn = get_db()
        cursor = conn.cursor()

        # 查找该SID对应的白板参与者并标记为离线
        # 注意：这里需要在连接时存储SID与用户的映射关系
        # 为简化，我们更新最近活跃的用户状态
        now = datetime.now().isoformat()
        cursor.execute("""
            UPDATE whiteboard_participants
            SET is_online = 0, left_at = ?
            WHERE last_active_at > datetime('now', '-10 minutes') AND is_online = 1
        """, (now,))

        conn.commit()
        conn.close()

    except Exception as e:
        print(f"清理白板用户状态失败: {e}")

@socketio.on('group_client_online')
def on_group_client_online(data):
    """
    当小组客户端上线时由其调用，用于注册自己。
    data 应该包含 {'ip': '...', 'hostname': '...'}
    """
    sid = request.sid
    ip = data.get('ip')
    hostname = data.get('hostname')
    if ip and hostname:
        group_clients[sid] = {'ip': ip, 'hostname': hostname}
        print(f"小组客户端已注册: {hostname} ({ip}), SID: {sid}")
    else:
        print(f"收到来自 {sid} 的无效小组客户端信息: {data}")

@socketio.on('join_course')
def on_join_course(data):
    """学生或小组客户端加入课程房间。"""
    course_id = data.get('course_id')
    if not course_id:
        print(f"[{request.sid}] 尝试加入课程但未提供 course_id")
        return

    room_name = f'course_{course_id}'
    
    # 检查是学生还是小组客户端
    user_type = session.get('user_type')
    user_id = session.get('user_id')

    if user_type in ['student', 'teacher'] and user_id:
        join_room(room_name)
        sid_to_room[request.sid] = room_name
        print(f"{user_type} {user_id} 已加入课程房间: {room_name}")
        
        # 检查是否有正在进行的计时器
        if course_id in active_timers and active_timers[course_id] > time.time():
            remaining_time = int(active_timers[course_id] - time.time())
            emit('start_timer', {'duration': remaining_time}, room=request.sid)
            print(f"向新加入的 {user_type} {user_id} 发送了剩余时间 {remaining_time} 秒")

    elif request.sid in group_clients:
        join_room(room_name)
        sid_to_room[request.sid] = room_name
        group_info = group_clients[request.sid]
        print(f"小组客户端 {group_info.get('hostname')} 已加入课程房间: {room_name}")
        
        # 同样为小组客户端检查计时器
        if course_id in active_timers and active_timers[course_id] > time.time():
            remaining_time = int(active_timers[course_id] - time.time())
            emit('start_timer', {'duration': remaining_time}, room=request.sid)
            print(f"向新加入的小组 {group_info.get('hostname')} 发送了剩余时间 {remaining_time} 秒")
    else:
        print(f"[{request.sid}] 未经身份验证的连接尝试加入课程 {course_id}")

# --- 聊天核心逻辑 ---
@socketio.on('send_message')
def on_send_message(data):
    """处理来自客户端的聊天消息"""
    if request.sid not in sid_to_room:
        print(f"错误: 收到来自 {request.sid} 的消息，但该用户未加入任何房间。")
        return
    room = sid_to_room[request.sid]

    message_text = data.get('message')
    if not message_text:
        return

    user_id = session.get('user_id')
    user_type = session.get('user_type')

    if not user_id or not user_type:
        print(f"错误: 来自 {request.sid} 的消息缺少认证信息。")
        return

    # 从数据库查询用户真实姓名
    db = get_db()
    cursor = db.cursor()
    user_name = user_id  # 默认使用ID

    try:
        if user_type == 'teacher':
            cursor.execute("SELECT name FROM teachers WHERE teacher_id = ?", (user_id,))
        elif user_type == 'student':
            cursor.execute("SELECT name FROM students WHERE student_id = ?", (user_id,))
        
        result = cursor.fetchone()
        if result:
            user_name = result[0]
    except Exception as e:
        print(f"数据库查询用户姓名时出错: {e}")
    finally:
        cursor.close()

    # 准备要广播的消息
    chat_data = {
        'user': user_name,
        'user_type': user_type,
        'message': message_text
    }
    
    # 将所有用户的消息都存入数据库
    try:
        # 从房间名 'course_...' 中提取 course_schedule_id
        course_schedule_id = room.split('_', 1)[1]
        
        db_conn = get_db()
        db_cursor = db_conn.cursor()
        
        current_time = datetime.now().isoformat()
        
        # 检查该用户在本堂课是否已有记录
        db_cursor.execute(
            "SELECT id, messages, message_count FROM danmaku WHERE user_id = ? AND course_schedule_id = ?",
            (user_id, course_schedule_id)
        )
        existing_record = db_cursor.fetchone()
        
        if existing_record:
            # 更新现有记录
            message_list = json.loads(existing_record['messages'])
            message_list.append({'time': current_time, 'text': message_text})
            
            db_cursor.execute(
                """
                UPDATE danmaku 
                SET messages = ?, message_count = ?, last_message_time = ?, updated_at = ?
                WHERE id = ?
                """,
                (
                    json.dumps(message_list, ensure_ascii=False),
                    existing_record['message_count'] + 1,
                    current_time,
                    current_time,
                    existing_record['id']
                )
            )
        else:
            # 插入新记录
            new_id = str(uuid.uuid4())
            message_list = [{'time': current_time, 'text': message_text}]
            
            db_cursor.execute(
                """
                INSERT INTO danmaku 
                (id, course_schedule_id, user_id, user_type, messages, message_count, first_message_time, last_message_time, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """,
                (
                    new_id,
                    course_schedule_id,
                    user_id,
                    user_type,
                    json.dumps(message_list, ensure_ascii=False),
                    1,
                    current_time,
                    current_time,
                    current_time,
                    current_time
                )
            )
        
        db_conn.commit()
        print(f"用户 {user_name} ({user_type}) 的消息已保存到数据库。")

    except Exception as e:
        print(f"保存消息到数据库时出错: {e}")
    finally:
        if 'db_conn' in locals() and db_conn:
            db_conn.close()

    # 向房间内的所有人广播
    emit('chat_message', chat_data, room=room, include_self=True)
    print(f"在房间 {room} 中广播消息: {chat_data}")


@socketio.on('leave_course')
def on_leave_course(data):
    course_id = data.get('course_id')
    if not course_id:
        return
    room_name = f'course_{course_id}'
    leave_room(room_name)
    if request.sid in sid_to_room:
        del sid_to_room[request.sid]
    user_id = session.get('user_id', request.sid)
    print(f"用户 {user_id} 已离开课程房间: {room_name}")

@socketio.on('broadcast_stream')
def on_broadcast_stream(data):
    """教师端广播指定流"""
    stream_name = data.get('stream_name')
    course_id = data.get('course_id') # 假设教师端会发送 course_id
    if stream_name and course_id:
        room_name = f'course_{course_id}'
        emit('broadcast_stream', {'stream_name': stream_name}, room=room_name)
        print(f"向课程 {course_id} 广播流: {stream_name}")

@socketio.on('drawing')
def on_drawing(data):
    """处理白板绘图数据"""
    course_id = data.get('course_id')
    if course_id:
        room = f'course_{course_id}'
        # 只转发给房间内的其他客户端，不包括发送者
        emit('drawing', data, room=room, include_self=False)

@socketio.on('student_drawing')
def handle_student_drawing(data):
    """学生向教师发送的绘图数据"""
    course_id = data.get('course_id')
    teacher_id = data.get('teacher_id') # 假设学生端知道教师ID
    if course_id and teacher_id:
        # 这里需要一个方法来找到教师的sid，或者教师加入一个特定的个人房间
        # 简单起见，我们假设教师也加入了course_id房间
        room = f'course_{course_id}'
        emit('student_drawing_to_teacher', data, room=room)

@socketio.on('request_help')
def on_request_help(data):
    """小组客户端请求帮助"""
    ip = data.get('ip')
    hostname = data.get('hostname')
    course_id = data.get('course_id') # 小组端需要知道自己在哪个课程
    
    if not all([ip, hostname, course_id]):
        print(f"收到无效的求助请求: {data}")
        return

    room_name = f'course_{course_id}'
    emit('help_request_from_group', {'ip': ip, 'hostname': hostname}, room=room_name)
    print(f"小组 {hostname} ({ip}) 在课程 {course_id} 中请求帮助")

@socketio.on('cancel_help')
def on_cancel_help(data):
    """小组客户端取消帮助请求"""
    ip = data.get('ip')
    hostname = data.get('hostname')
    course_id = data.get('course_id')
    
    if not all([ip, hostname, course_id]):
        return

    room_name = f'course_{course_id}'
    emit('help_request_cancelled', {'ip': ip, 'hostname': hostname}, room=room_name)
    print(f"小组 {hostname} ({ip}) 在课程 {course_id} 中取消了帮助请求")

@socketio.on('student_answer')
def on_student_answer(data):
    """接收学生答案并转发给教师"""
    course_id = data.get('course_schedule_id')
    if not course_id:
        # 兼容旧版或不同命名
        course_id = data.get('course_id')
    
    if course_id:
        room_name = f'course_{course_id}'
        # 只发给教师，但简单起见，先发给整个房间，由教师端UI过滤
        emit('answer_submitted', data, room=room_name)
        print(f"学生 {data.get('student_id')} 在课程 {course_id} 中提交了答案")
    else:
        print(f"收到学生答案但没有课程ID: {data}")
# --- 学员端与小组端直接通信 ---

@socketio.on('student_join_group')
def handle_student_join_group(data):
    """学生加入小组"""
    student_id = data.get('student_id')
    student_name = data.get('student_name')
    group_id = data.get('group_id')

    if not all([student_id, student_name, group_id]):
        emit('join_group_response', {'success': False, 'error': '缺少必要参数'})
        return

    # 检查小组是否在线
    target_sid = group_id_to_sid.get(group_id)
    if not target_sid:
        emit('join_group_response', {'success': False, 'error': '小组不在线'})
        return

    # 通知小组端有学生加入
    emit('student_joined', {
        'student_id': student_id,
        'student_name': student_name,
        'student_sid': request.sid
    }, room=target_sid)

    # 回复学生端
    emit('join_group_response', {'success': True, 'group_id': group_id})
    print(f"学生 {student_name} ({student_id}) 加入小组 {group_id}")

@socketio.on('student_leave_group')
def handle_student_leave_group(data):
    """学生离开小组"""
    student_id = data.get('student_id')
    group_id = data.get('group_id')

    if not all([student_id, group_id]):
        return

    # 通知小组端学生离开
    target_sid = group_id_to_sid.get(group_id)
    if target_sid:
        emit('student_left', {
            'student_id': student_id,
            'student_sid': request.sid
        }, room=target_sid)

    print(f"学生 {student_id} 离开小组 {group_id}")

@socketio.on('request_screen_share')
def handle_request_screen_share(data):
    """学生请求投屏到小组端"""
    student_id = data.get('student_id')
    student_name = data.get('student_name')
    group_id = data.get('group_id')
    stream_type = data.get('stream_type', 'screen')  # screen, camera, file

    if not all([student_id, student_name, group_id]):
        emit('screen_share_response', {'success': False, 'error': '缺少必要参数'})
        return

    # 检查小组是否在线
    target_sid = group_id_to_sid.get(group_id)
    if not target_sid:
        emit('screen_share_response', {'success': False, 'error': '小组不在线'})
        return

    # 生成流名称
    stream_name = f"student_{student_id}_{int(time.time())}"

    # 通知小组端有投屏请求
    emit('screen_share_request', {
        'student_id': student_id,
        'student_name': student_name,
        'student_sid': request.sid,
        'stream_name': stream_name,
        'stream_type': stream_type
    }, room=target_sid)

    # 回复学生端流信息
    emit('screen_share_response', {
        'success': True,
        'stream_name': stream_name,
        'rtsp_url': f"rtsp://localhost:8554/{stream_name}"
    })

    print(f"学生 {student_name} 请求向小组 {group_id} 投屏")

@socketio.on('stop_screen_share')
def handle_stop_screen_share(data):
    """学生停止投屏"""
    student_id = data.get('student_id')
    group_id = data.get('group_id')
    stream_name = data.get('stream_name')

    if not all([student_id, group_id]):
        return

    # 通知小组端停止投屏
    target_sid = group_id_to_sid.get(group_id)
    if target_sid:
        emit('screen_share_stopped', {
            'student_id': student_id,
            'stream_name': stream_name
        }, room=target_sid)

    print(f"学生 {student_id} 停止向小组 {group_id} 投屏")

@socketio.on('collaborative_whiteboard_join')
def handle_collaborative_whiteboard_join(data):
    """学生加入协同白板，管理会话状态"""
    student_id = data.get('student_id')
    student_name = data.get('student_name')
    group_id = data.get('group_id')
    course_schedule_id = data.get('course_schedule_id')

    if not all([student_id, student_name, group_id]):
        emit('whiteboard_join_response', {'success': False, 'error': '缺少必要参数'})
        return

    try:
        # 获取或创建白板会话
        session_id = whiteboard_service.get_session_by_group(group_id, course_schedule_id)
        if not session_id:
            # 创建新会话
            session_id = whiteboard_service.create_session(
                group_id, course_schedule_id, f"小组{group_id}白板", student_id
            )
            if not session_id:
                emit('whiteboard_join_response', {'success': False, 'error': '创建会话失败'})
                return

        # 加入房间
        join_room(f"whiteboard_{session_id}")

        # 用户加入会话
        success = whiteboard_service.join_session(session_id, student_id, 'student', student_name)
        if not success:
            emit('whiteboard_join_response', {'success': False, 'error': '加入会话失败'})
            return

        # 获取当前内容和参与者
        content = whiteboard_service.get_session_content(session_id)
        participants = whiteboard_service.get_session_participants(session_id)

        # 向新加入者发送完整状态
        emit('whiteboard_join_response', {
            'success': True,
            'session_id': session_id,
            'content': content,
            'participants': participants
        }, room=request.sid)

        # 向其他人广播新用户加入
        emit('whiteboard_user_joined', {
            'user_id': student_id,
            'user_name': student_name,
            'user_type': 'student'
        }, room=f"whiteboard_{session_id}", include_self=False)

        print(f"学生 {student_name} 加入白板会话 {session_id}")

    except Exception as e:
        print(f"处理白板加入失败: {e}")
        emit('whiteboard_join_response', {'success': False, 'error': '服务器错误'})

@socketio.on('collaborative_whiteboard_operation')
def handle_collaborative_whiteboard_operation(data):
    """处理协同白板操作并广播给同组成员"""
    session_id = data.get('session_id')
    user_id = data.get('user_id')
    user_type = data.get('user_type', 'student')
    operation_type = data.get('operation_type')
    operation_data = data.get('operation_data')

    if not all([session_id, user_id, operation_type, operation_data]):
        return

    try:
        # 记录操作
        whiteboard_service.record_operation(
            session_id, user_id, user_type, operation_type, operation_data
        )

        # 广播操作给其他用户
        emit('whiteboard_operation', {
            'session_id': session_id,
            'user_id': user_id,
            'user_type': user_type,
            'operation_type': operation_type,
            'operation_data': operation_data,
            'timestamp': int(time.time() * 1000)
        }, room=f"whiteboard_{session_id}", include_self=False)

    except Exception as e:
        print(f"处理白板操作失败: {e}")

@socketio.on('whiteboard_update')
def handle_whiteboard_update(data):
    """处理来自客户端的Excalidraw白板更新，并保存状态"""
    session_id = data.get('session_id')
    user_id = data.get('user_id')
    elements = data.get('elements', [])
    app_state = data.get('appState', {})

    if not session_id:
        # 兼容旧版本，使用房间名
        room = None
        for r in request.rooms():
            if r != request.sid and not r.startswith('whiteboard_'):
                room = r
                break

        if room:
            # 保存最新状态（旧版本兼容）
            whiteboard_states[room] = data
            # 广播给其他人
            emit('whiteboard_update', data, room=room, include_self=False)
        return

    try:
        # 保存内容到数据库
        whiteboard_service.save_content(session_id, elements, app_state, user_id)

        # 广播更新给其他用户
        emit('whiteboard_update', {
            'session_id': session_id,
            'elements': elements,
            'appState': app_state,
            'user_id': user_id,
            'timestamp': int(time.time() * 1000)
        }, room=f"whiteboard_{session_id}", include_self=False)

    except Exception as e:
        print(f"处理白板更新失败: {e}")

@socketio.on('join_whiteboard')
def handle_join_whiteboard(data):
    """处理用户加入白板的请求，并同步当前状态"""
    room = data.get('room')
    session_id = data.get('session_id')

    if session_id:
        # 新版本：使用会话ID
        join_room(f"whiteboard_{session_id}")

        # 获取最新内容
        content = whiteboard_service.get_session_content(session_id)
        if content:
            emit('whiteboard_update', {
                'session_id': session_id,
                'elements': content.get('elements', []),
                'appState': content.get('appState', {})
            }, room=request.sid)

        print(f"客户端 {request.sid} 已加入白板会话 {session_id}")

    elif room:
        # 旧版本兼容
        join_room(room)
        # 如果有历史状态，发送给新加入的用户
        if room in whiteboard_states:
            emit('whiteboard_update', whiteboard_states[room], room=request.sid)
        print(f"客户端 {request.sid} 已加入白板房间 {room}")

@socketio.on('whiteboard_leave')
def handle_whiteboard_leave(data):
    """处理用户离开白板"""
    session_id = data.get('session_id')
    user_id = data.get('user_id')

    if session_id and user_id:
        try:
            # 用户离开会话
            whiteboard_service.leave_session(session_id, user_id)

            # 离开房间
            leave_room(f"whiteboard_{session_id}")

            # 通知其他用户
            emit('whiteboard_user_left', {
                'user_id': user_id,
                'session_id': session_id
            }, room=f"whiteboard_{session_id}")

            print(f"用户 {user_id} 离开白板会话 {session_id}")

        except Exception as e:
            print(f"处理用户离开白板失败: {e}")

@socketio.on('whiteboard_cursor_update')
def handle_whiteboard_cursor_update(data):
    """处理白板光标位置更新"""
    session_id = data.get('session_id')
    user_id = data.get('user_id')
    cursor_data = data.get('cursor_data')

    if session_id and user_id and cursor_data:
        # 广播光标位置给其他用户
        emit('whiteboard_cursor_update', {
            'user_id': user_id,
            'cursor_data': cursor_data
        }, room=f"whiteboard_{session_id}", include_self=False)

@socketio.on('whiteboard_user_selection')
def handle_whiteboard_user_selection(data):
    """处理用户选择元素"""
    session_id = data.get('session_id')
    user_id = data.get('user_id')
    selected_elements = data.get('selected_elements', [])

    if session_id and user_id:
        # 广播用户选择状态
        emit('whiteboard_user_selection', {
            'user_id': user_id,
            'selected_elements': selected_elements
        }, room=f"whiteboard_{session_id}", include_self=False)

@socketio.on('whiteboard_collaboration_toggle')
def handle_whiteboard_collaboration_toggle(data):
    """处理协作模式切换（教师权限）"""
    session_id = data.get('session_id')
    enabled = data.get('enabled', True)
    user_id = data.get('user_id')
    user_type = data.get('user_type')

    # 只有教师可以切换协作模式
    if user_type != 'teacher':
        emit('whiteboard_error', {'message': '权限不足'}, room=request.sid)
        return

    if session_id:
        try:
            # 更新协作状态
            success = whiteboard_service.update_collaboration_status(session_id, enabled)
            if success:
                # 通知所有参与者
                emit('whiteboard_collaboration_status', {
                    'session_id': session_id,
                    'enabled': enabled
                }, room=f"whiteboard_{session_id}")

                print(f"白板会话 {session_id} 协作模式: {'开启' if enabled else '关闭'}")
            else:
                emit('whiteboard_error', {'message': '更新协作状态失败'}, room=request.sid)

        except Exception as e:
            print(f"切换协作模式失败: {e}")
            emit('whiteboard_error', {'message': '服务器错误'}, room=request.sid)

@socketio.on('share_file_to_group')
def handle_share_file_to_group(data):
    """学生向小组分享文件"""
    student_id = data.get('student_id')
    student_name = data.get('student_name')
    group_id = data.get('group_id')
    file_info = data.get('file_info')  # 包含文件名、路径、类型等信息

    if not all([student_id, student_name, group_id, file_info]):
        emit('file_share_response', {'success': False, 'error': '缺少必要参数'})
        return

    # 通知小组端有文件分享
    target_sid = group_id_to_sid.get(group_id)
    if target_sid:
        emit('file_shared', {
            'student_id': student_id,
            'student_name': student_name,
            'file_info': file_info,
            'shared_at': datetime.now().isoformat()
        }, room=target_sid)

        emit('file_share_response', {'success': True})
        print(f"学生 {student_name} 向小组 {group_id} 分享文件: {file_info.get('filename')}")
    else:
        emit('file_share_response', {'success': False, 'error': '小组不在线'})

@socketio.on('share_artwork_to_group')
def handle_share_artwork_to_group(data):
    """学生向小组分享作品"""
    student_id = data.get('student_id')
    student_name = data.get('student_name')
    group_id = data.get('group_id')
    artwork_info = data.get('artwork_info')  # 包含作品信息

    if not all([student_id, student_name, group_id, artwork_info]):
        emit('artwork_share_response', {'success': False, 'error': '缺少必要参数'})
        return

    # 通知小组端有作品分享
    target_sid = group_id_to_sid.get(group_id)
    if target_sid:
        emit('artwork_shared', {
            'student_id': student_id,
            'student_name': student_name,
            'artwork_info': artwork_info,
            'shared_at': datetime.now().isoformat()
        }, room=target_sid)

        emit('artwork_share_response', {'success': True})
        print(f"学生 {student_name} 向小组 {group_id} 分享作品")
    else:
        emit('artwork_share_response', {'success': False, 'error': '小组不在线'})

# --- WebRTC投屏相关事件 ---

@socketio.on('webrtc_screen_offer')
def handle_webrtc_screen_offer(data):
    """处理学生端WebRTC投屏Offer"""
    from app.services.rtsp_service import webrtc_bridge

    stream_name = data.get('stream_name')
    student_id = data.get('student_id')
    offer = data.get('offer')

    if not all([stream_name, student_id, offer]):
        emit('webrtc_screen_answer', {'success': False, 'error': '缺少必要参数'})
        return

    # 创建WebRTC到RTSP桥接
    bridge_info = webrtc_bridge.create_bridge(stream_name, student_id)

    if bridge_info:
        # 处理WebRTC Offer
        answer = webrtc_bridge.handle_webrtc_offer(stream_name, offer)

        if answer:
            emit('webrtc_screen_answer', {
                'success': True,
                'answer': answer,
                'rtsp_url': bridge_info['rtsp_url']
            })
        else:
            emit('webrtc_screen_answer', {'success': False, 'error': '处理Offer失败'})
    else:
        emit('webrtc_screen_answer', {'success': False, 'error': '创建桥接失败'})

@socketio.on('webrtc_screen_ice_candidate')
def handle_webrtc_screen_ice_candidate(data):
    """处理学生端WebRTC投屏ICE候选"""
    from app.services.rtsp_service import webrtc_bridge

    stream_name = data.get('stream_name')
    candidate = data.get('candidate')

    if stream_name and candidate:
        success = webrtc_bridge.handle_ice_candidate(stream_name, candidate)
        emit('webrtc_screen_ice_response', {'success': success})

@socketio.on('stop_webrtc_screen_share')
def handle_stop_webrtc_screen_share(data):
    """停止WebRTC投屏"""
    from app.services.rtsp_service import webrtc_bridge

    stream_name = data.get('stream_name')
    student_id = data.get('student_id')

    if stream_name:
        success = webrtc_bridge.remove_bridge(stream_name)
        emit('webrtc_screen_stop_response', {'success': success})

        # 通知小组端停止投屏
        group_id = data.get('group_id')
        if group_id:
            target_sid = group_id_to_sid.get(group_id)
            if target_sid:
                emit('screen_share_stopped', {
                    'student_id': student_id,
                    'stream_name': stream_name
                }, room=target_sid)

@socketio.on('join_group_room')
def handle_join_group_room(data):
    """
    小组端加入自己的房间，以便接收来自学员的定向消息。
    """
    group_id = data.get('group_id')
    if group_id:
        join_room(group_id)
        group_id_to_sid[group_id] = request.sid # 建立 group_id 到 sid 的映射
        
        # 获取小组客户端信息用于日志
        client_info = group_clients.get(request.sid, {})
        hostname = client_info.get('hostname', 'Unknown')
        ip = client_info.get('ip', 'Unknown')
        
        print(f"小组客户端 {request.sid} ({hostname}/{ip}) 已加入房间: {group_id} 并建立映射")
        print(f"当前group_id_to_sid映射数量: {len(group_id_to_sid)}")
        print(f"映射详情: {dict(group_id_to_sid)}")
        
        # 确认映射建立成功
        if group_id in group_id_to_sid and group_id_to_sid[group_id] == request.sid:
            print(f"✅ group_id映射确认成功: {group_id} -> {request.sid}")
        else:
            print(f"❌ group_id映射确认失败: {group_id}")

@socketio.on('send_to_group')
def handle_send_to_group(data):
    """
    从学员端接收消息，并将其转发到指定的小组房间。
    """
    group_id = data.get('group_id')
    message = data.get('message')
    if group_id and message:
        # 向指定的小组房间广播 'message_from_student' 事件
        socketio.emit('message_from_student', {'message': message}, room=group_id)
        print(f"已将消息 '{message}' 转发到小组房间 {group_id}")

# --- WebRTC 信令服务器事件 ---

@socketio.on('webrtc_offer')
def handle_webrtc_offer(data):
    group_id = data.get('group_id')
    target_sid = group_id_to_sid.get(group_id)
    if not target_sid:
        print(f"错误: 找不到小组ID {group_id} 对应的SID")
        return
    
    emit('webrtc_offer', {'offer': data.get('offer'), 'student_sid': request.sid}, room=target_sid)
    print(f"已将来自 {request.sid} 的WebRTC offer转发到小组 {group_id} (SID: {target_sid})")

@socketio.on('webrtc_answer')
def handle_webrtc_answer(data):
    student_sid = data.get('student_sid')
    if not student_sid:
        return
    emit('webrtc_answer', {'answer': data.get('answer')}, room=student_sid)
    print(f"已将来自小组 {request.sid} 的WebRTC answer转发到学员 {student_sid}")

@socketio.on('webrtc_ice_candidate')
def handle_webrtc_ice_candidate(data):
    candidate = data.get('candidate')
    
    # 判断转发目标
    student_sid = data.get('student_sid') # 从小组端发往学员端
    group_id = data.get('group_id')       # 从学员端发往小组端

    if student_sid:
        emit('webrtc_ice_candidate', {'candidate': candidate}, room=student_sid)
        # print(f"ICE Candidate from group {request.sid} relayed to student {student_sid}")
    elif group_id:
        target_sid = group_id_to_sid.get(group_id)
        if target_sid:
            emit('webrtc_ice_candidate', {'candidate': candidate, 'sender_sid': request.sid}, room=target_sid)
            # print(f"ICE Candidate from student {request.sid} relayed to group {group_id}")
        else:
            print(f"错误: 转发ICE candidate时找不到小组ID {group_id} 对应的SID")

# --- 触控回传功能事件处理 ---

@socketio.on('touch_control_command')
def handle_touch_control_command(data):
    """处理教师端发送的触控控制命令"""
    group_id = data.get('group_id')
    action = data.get('action')
    
    if not group_id or not action:
        print(f"收到无效的触控控制命令: {data}")
        return
    
    # 调试信息：显示当前映射状态
    print(f"触控命令请求: group_id={group_id}, action={action}")
    print(f"当前group_id_to_sid映射: {dict(group_id_to_sid)}")
    print(f"当前在线小组数量: {len(group_clients)}")
    
    # 查找目标小组的SID
    target_sid = group_id_to_sid.get(group_id)
    if not target_sid:
        print(f"错误: 找不到小组ID {group_id} 对应的SID")
        print(f"可用的group_id列表: {list(group_id_to_sid.keys())}")
        
        # 尝试从在线小组客户端中查找匹配的group_id
        potential_matches = []
        for sid, client_info in group_clients.items():
            hostname = client_info.get('hostname', '')
            ip = client_info.get('ip', '')
            potential_group_id = f"group_{hostname.replace(' ', '_')}_{ip.replace('.', '_')}"
            potential_matches.append(potential_group_id)
            if potential_group_id == group_id:
                print(f"找到匹配的小组客户端，但映射缺失。尝试重建映射...")
                group_id_to_sid[group_id] = sid
                target_sid = sid
                break
        
        if not target_sid:
            print(f"可能的group_id匹配: {potential_matches}")
            emit('touch_control_error', {
                'error': f'Group {group_id} not found',
                'group_id': group_id,
                'available_groups': list(group_id_to_sid.keys()),
                'potential_matches': potential_matches
            })
            return
    
    # 转发命令到目标小组端
    emit('touch_control_command', data, room=target_sid)
    print(f"已将触控控制命令 '{action}' 转发到小组 {group_id} (SID: {target_sid})")

@socketio.on('touch_control_event')
def handle_touch_control_event(data):
    """处理教师端发送的触控事件"""
    group_id = data.get('group_id')
    action = data.get('action')
    
    if not group_id or not action:
        print(f"收到无效的触控事件: {data}")
        return
    
    # 查找目标小组的SID
    target_sid = group_id_to_sid.get(group_id)
    if not target_sid:
        print(f"错误: 找不到小组ID {group_id} 对应的SID")
        return
    
    # 转发事件到目标小组端
    emit('touch_control_event', data, room=target_sid)
    # print(f"已将触控事件 '{action}' 转发到小组 {group_id}")

@socketio.on('touch_control_response')
def handle_touch_control_response(data):
    """处理小组端的触控控制响应"""
    teacher_id = data.get('teacher_id')
    course_id = data.get('course_id')
    
    if course_id:
        # 转发响应到课程房间（主要是教师端）
        room_name = f'course_{course_id}'
        emit('touch_control_response', data, room=room_name)
        print(f"已将触控控制响应转发到课程 {course_id}")
    else:
        print(f"收到无效的触控控制响应: {data}")

@socketio.on('virtual_keyboard_status')
def handle_virtual_keyboard_status(data):
    """处理虚拟键盘状态变化"""
    group_id = data.get('group_id')
    course_id = data.get('course_id')
    status = data.get('status')  # 'shown', 'hidden', 'error'
    
    if course_id:
        room_name = f'course_{course_id}'
        emit('virtual_keyboard_status', data, room=room_name)
        print(f"小组 {group_id} 虚拟键盘状态: {status}")
    else:
        print(f"收到无效的虚拟键盘状态: {data}")

@socketio.on('group_drawing_event')
def handle_group_drawing_event(data):
    """处理来自小组端的绘图事件并转发给教师"""
    # 假设教师已经加入了课程房间
    course_id = data.get('course_id')
    if course_id:
        room_name = f'course_{course_id}'
        emit('group_drawing_to_teacher', data, room=room_name)
    else:
        print(f"收到小组绘图事件但没有课程ID: {data}")

@socketio.on('group_whiteboard_sync')
def handle_group_whiteboard_sync(data):
    """处理来自小组端的完整白板状态并转发给教师"""
    course_id = data.get('course_id')
    if course_id:
        room_name = f'course_{course_id}'
        # 附加上发送方的小组信息
        data['group_sid'] = request.sid
        emit('whiteboard_state_from_group', data, room=room_name)
    else:
            print(f"收到小组白板同步事件但没有课程ID: {data}")

@socketio.on('start_collaboration')
def handle_start_collaboration(data):
    """处理教师端发送的开始协作指令"""
    course_id = data.get('course_id')
    if course_id:
        room_name = f'course_{course_id}'
        emit('collaboration_started', {}, room=room_name)
        print(f"课程 {course_id} 已开启白板协作")

@socketio.on('stop_collaboration')
def handle_stop_collaboration(data):
    """处理教师端发送的停止协作指令"""
    course_id = data.get('course_id')
    if course_id:
        room_name = f'course_{course_id}'
        emit('collaboration_stopped', {}, room=room_name)
        print(f"课程 {course_id} 已停止白板协作")

@socketio.on('teacher_drawing_event')
def handle_teacher_drawing_event(data):
    """处理教师端发送的绘图事件"""
    course_id = data.get('course_id')
    if course_id:
        room_name = f'course_{course_id}'
        emit('teacher_drawing_event', data, room=room_name, include_self=False)
        # print(f"已将教师绘图事件转发到课程 {course_id}")

@socketio.on('leave_course')
def handle_leave_course(data):
    """处理离开课程房间"""
    course_id = data.get('course_id')
    if course_id:
        room_name = f'course_{course_id}'
        leave_room(room_name)
        print(f"客户端 {request.sid} 已离开课程房间: {room_name}")

@socketio.on('shutdown_command')
def handle_shutdown_command(data):
    """处理来自教师端的关机命令"""
    group_id = data.get('group_id')
    if group_id:
        target_sid = group_id_to_sid.get(group_id)
        if target_sid:
            emit('shutdown_command', {}, room=target_sid)
            print(f"已向小组 {group_id} (SID: {target_sid}) 转发关机命令")
        else:
            print(f"错误: 转发关机命令时找不到小组ID {group_id} 对应的SID")