import sqlite3
import os
import json
def get_db(database_path=None):
    """获取数据库连接"""
    if database_path is None:
        from flask import current_app
        database_path = current_app.config['DATABASE_PATH']

    conn = sqlite3.connect(database_path, timeout=30.0)
    conn.row_factory = sqlite3.Row

    # 设置WAL模式以提高并发性能
    conn.execute('PRAGMA journal_mode=WAL')
    # 设置同步模式
    conn.execute('PRAGMA synchronous=NORMAL')
    # 设置忙等待超时
    conn.execute('PRAGMA busy_timeout=30000')
    # 启用外键约束
    conn.execute('PRAGMA foreign_keys=ON')

    return conn

def init_db(database_path):
    """初始化数据库"""
    # 确保数据目录存在
    data_dir = os.path.dirname(database_path)
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)

    conn = sqlite3.connect(database_path)
    cursor = conn.cursor()

    # 创建数据库表
    create_tables(cursor, conn)

    # 插入初始数据
    insert_initial_data(cursor)

    conn.commit()
    conn.close()

def create_tables(cursor, conn=None):
    """创建数据库表"""

    # 创建教室表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS classrooms (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL UNIQUE,
            capacity INTEGER NOT NULL,
            location TEXT,
            created_at TEXT NOT NULL
        )
    ''')

    # 创建弹幕表 - 现在是通用消息表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS danmaku (
            id TEXT PRIMARY KEY,
            course_schedule_id TEXT NOT NULL,
            user_id TEXT NOT NULL,
            user_type TEXT NOT NULL, -- 'student' or 'teacher'
            messages TEXT NOT NULL,
            message_count INTEGER DEFAULT 0,
            first_message_time TEXT,
            last_message_time TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            UNIQUE(course_schedule_id, user_id),
            FOREIGN KEY (course_schedule_id) REFERENCES course_schedules(id)
        )
    ''')

    # 创建班级表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS classes (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL UNIQUE,
            grade TEXT NOT NULL,
            created_at TEXT NOT NULL
        )
    ''')

    # 创建课程表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS courses (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            code TEXT NOT NULL UNIQUE,
            description TEXT,
            created_at TEXT NOT NULL
        )
    ''')

    # 创建教师表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS teachers (
            teacher_id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            password TEXT NOT NULL,
            email TEXT,
            phone TEXT,
            created_at TEXT NOT NULL
        )
    ''')

    # 创建学生表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS students (
            student_id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            password TEXT NOT NULL,
            gender TEXT NOT NULL,
            class_id TEXT NOT NULL,
            email TEXT,
            phone TEXT,
            created_at TEXT NOT NULL,
            FOREIGN KEY (class_id) REFERENCES classes(id)
        )
    ''')

    # 创建课程安排表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS course_schedules (
            id TEXT PRIMARY KEY,
            course_id TEXT NOT NULL,
            teacher_id TEXT NOT NULL,
            classroom_id TEXT NOT NULL,
            class_id TEXT NOT NULL,
            day_of_week INTEGER NOT NULL,
            start_time TEXT NOT NULL,
            end_time TEXT NOT NULL,
            status TEXT DEFAULT 'scheduled',
            start_datetime TEXT,
            end_datetime TEXT,
            description TEXT,
            created_at TEXT NOT NULL,
            FOREIGN KEY (course_id) REFERENCES courses(id),
            FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id),
            FOREIGN KEY (classroom_id) REFERENCES classrooms(id),
            FOREIGN KEY (class_id) REFERENCES classes(id)
        )
    ''')

    # 创建考勤表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS class_attendance (
            id TEXT PRIMARY KEY,
            course_schedule_id TEXT NOT NULL,
            student_id TEXT NOT NULL,
            signin_time TEXT,
            status TEXT DEFAULT 'absent',
            remark TEXT,
            created_at TEXT NOT NULL,
            FOREIGN KEY (course_schedule_id) REFERENCES course_schedules(id),
            FOREIGN KEY (student_id) REFERENCES students(student_id)
        )
    ''')

    # 创建作业表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS homework (
            id TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            description TEXT,
            teacher_id TEXT NOT NULL,
            course_schedule_id TEXT,
            created_at TEXT NOT NULL,
            data TEXT NOT NULL,
            status TEXT DEFAULT 'draft',
            deadline TEXT,
            type TEXT DEFAULT 'homework',
            question_count INTEGER DEFAULT 0,
            submitted_count INTEGER DEFAULT 0,
            total_students INTEGER DEFAULT 0,
            FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id),
            FOREIGN KEY (course_schedule_id) REFERENCES course_schedules(id)
        )
    ''')

    # 创建试卷表（资源库用）
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS papers (
            id TEXT PRIMARY KEY,
            title TEXT NOT NULL,
            description TEXT,
            teacher_id TEXT NOT NULL,
            created_at TEXT NOT NULL,
            data TEXT NOT NULL,
            FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id)
        )
    ''')
    
    # 创建作业成绩表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS homework_results (
            id TEXT PRIMARY KEY,
            homework_id TEXT NOT NULL,
            student_id TEXT NOT NULL,
            score REAL NOT NULL,
            data TEXT NOT NULL,
            submitted_at TEXT NOT NULL,
            answers TEXT,
            FOREIGN KEY (homework_id) REFERENCES homework(id),
            FOREIGN KEY (student_id) REFERENCES students(student_id)
        )
    ''')
    
    # 创建互动答题提交表（支持逐题提交）
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS homework_submissions (
            id TEXT PRIMARY KEY,
            homework_id TEXT NOT NULL,
            student_id TEXT NOT NULL,
            question_id TEXT NOT NULL,
            answer TEXT NOT NULL,
            submitted_at TEXT NOT NULL,
            UNIQUE(homework_id, student_id, question_id),
            FOREIGN KEY (homework_id) REFERENCES homework(id),
            FOREIGN KEY (student_id) REFERENCES students(student_id)
        )
    ''')

    # 创建新的文件夹表 (t_folders)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS t_folders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            folder_name VARCHAR(255) NOT NULL,
            parent_id INTEGER DEFAULT NULL,
            teacher_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES t_folders(id) ON DELETE CASCADE,
            FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id)
        )
    ''')

    # 创建新的文件表 (t_files)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS t_files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            original_filename VARCHAR(255) NOT NULL,
            stored_filename VARCHAR(255) NOT NULL UNIQUE,
            file_path VARCHAR(255) NOT NULL,
            file_size BIGINT NOT NULL,
            mime_type VARCHAR(100),
            folder_id INTEGER DEFAULT NULL,
            teacher_id INTEGER NOT NULL,
            course_schedule_id TEXT,
            upload_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (folder_id) REFERENCES t_folders(id) ON DELETE SET NULL,
            FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id),
            FOREIGN KEY (course_schedule_id) REFERENCES course_schedules(id)
        )
    ''')

    # 创建课堂分组表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS class_groups (
            id TEXT PRIMARY KEY,
            course_schedule_id TEXT NOT NULL,
            group_name TEXT NOT NULL,
            group_description TEXT,
            created_by TEXT NOT NULL,
            created_at TEXT NOT NULL,
            FOREIGN KEY (course_schedule_id) REFERENCES course_schedules(id),
            FOREIGN KEY (created_by) REFERENCES teachers(teacher_id)
        )
    ''')

    # 创建分组成员表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS group_members (
            id TEXT PRIMARY KEY,
            group_id TEXT NOT NULL,
            student_id TEXT NOT NULL,
            joined_at TEXT NOT NULL,
            UNIQUE(group_id, student_id),
            FOREIGN KEY (group_id) REFERENCES class_groups(id),
            FOREIGN KEY (student_id) REFERENCES students(student_id)
        )
    ''')

    # 创建习题表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS exercises (
            id TEXT PRIMARY KEY,
            teacher_id TEXT NOT NULL,
            type TEXT NOT NULL,
            question TEXT NOT NULL,
            options TEXT,
            answer TEXT NOT NULL,
            difficulty TEXT NOT NULL,
            folder_path TEXT DEFAULT '/',
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id)
        )
    ''')

    # 创建课堂报告表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS class_reports (
            id TEXT PRIMARY KEY,
            course_schedule_id TEXT NOT NULL,
            teacher_id TEXT NOT NULL,
            report_date TEXT NOT NULL,
            attendance_count INTEGER DEFAULT 0,
            total_students INTEGER DEFAULT 0,
            group_count INTEGER DEFAULT 0,
            homework_count INTEGER DEFAULT 0,
            report_content TEXT,
            remarks TEXT,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            FOREIGN KEY (course_schedule_id) REFERENCES course_schedules(id),
            FOREIGN KEY (teacher_id) REFERENCES teachers(teacher_id)
        )
    ''')

    # 创建白板会话表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS whiteboard_sessions (
            id TEXT PRIMARY KEY,
            group_id TEXT NOT NULL,
            course_schedule_id TEXT NOT NULL,
            session_name TEXT NOT NULL,
            created_by TEXT NOT NULL,
            status TEXT DEFAULT 'active',
            collaboration_enabled BOOLEAN DEFAULT 1,
            max_participants INTEGER DEFAULT 50,
            created_at TEXT NOT NULL,
            updated_at TEXT NOT NULL,
            ended_at TEXT,
            FOREIGN KEY (group_id) REFERENCES class_groups(id),
            FOREIGN KEY (course_schedule_id) REFERENCES course_schedules(id),
            FOREIGN KEY (created_by) REFERENCES teachers(teacher_id)
        )
    ''')

    # 创建白板内容表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS whiteboard_contents (
            id TEXT PRIMARY KEY,
            session_id TEXT NOT NULL,
            content_data TEXT NOT NULL,
            app_state TEXT,
            version INTEGER DEFAULT 1,
            created_by TEXT,
            created_at TEXT NOT NULL,
            is_snapshot BOOLEAN DEFAULT 0,
            FOREIGN KEY (session_id) REFERENCES whiteboard_sessions(id)
        )
    ''')

    # 创建白板操作记录表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS whiteboard_operations (
            id TEXT PRIMARY KEY,
            session_id TEXT NOT NULL,
            user_id TEXT NOT NULL,
            user_type TEXT NOT NULL,
            operation_type TEXT NOT NULL,
            operation_data TEXT NOT NULL,
            timestamp INTEGER NOT NULL,
            sequence_number INTEGER NOT NULL,
            created_at TEXT NOT NULL,
            FOREIGN KEY (session_id) REFERENCES whiteboard_sessions(id)
        )
    ''')

    # 创建白板参与者表
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS whiteboard_participants (
            id TEXT PRIMARY KEY,
            session_id TEXT NOT NULL,
            user_id TEXT NOT NULL,
            user_type TEXT NOT NULL,
            user_name TEXT NOT NULL,
            role TEXT DEFAULT 'editor',
            joined_at TEXT NOT NULL,
            last_active_at TEXT,
            left_at TEXT,
            is_online BOOLEAN DEFAULT 1,
            UNIQUE(session_id, user_id),
            FOREIGN KEY (session_id) REFERENCES whiteboard_sessions(id)
        )
    ''')

    # 互动答题结果表 (用于逐题提交的模式)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS quiz_results (
            id TEXT PRIMARY KEY,
            homework_id TEXT NOT NULL,
            student_id TEXT NOT NULL,
            question_id TEXT NOT NULL,
            answer TEXT NOT NULL,
            is_correct BOOLEAN NOT NULL,
            submitted_at TEXT NOT NULL,
            UNIQUE(homework_id, student_id, question_id),
            FOREIGN KEY (homework_id) REFERENCES homework(id),
            FOREIGN KEY (student_id) REFERENCES students(student_id)
        )
    ''')
  
def insert_initial_data(cursor):
    """插入初始数据"""
    import hashlib
    from datetime import datetime
    import uuid

    current_time = datetime.now().isoformat()

    # 检查是否已有数据
    cursor.execute("SELECT COUNT(*) FROM teachers")
    if cursor.fetchone()[0] > 0:
        return  # 已有数据，不需要重复插入

    # 插入默认教室
    classrooms = [
        ('CR001', '教室101', 50, '教学楼A栋1楼'),
        ('CR002', '教室102', 50, '教学楼A栋1楼'),
        ('CR003', '教室201', 60, '教学楼A栋2楼'),
    ]

    for classroom_id, name, capacity, location in classrooms:
        cursor.execute(
            "INSERT OR IGNORE INTO classrooms (id, name, capacity, location, created_at) VALUES (?, ?, ?, ?, ?)",
            (classroom_id, name, capacity, location, current_time)
        )

    # 插入默认班级
    classes = [
        ('CL001', '计算机科学1班', '2023级'),
        ('CL002', '计算机科学2班', '2023级'),
        ('CL003', '软件工程1班', '2023级'),
    ]

    for class_id, name, grade in classes:
        cursor.execute(
            "INSERT OR IGNORE INTO classes (id, name, grade, created_at) VALUES (?, ?, ?, ?)",
            (class_id, name, grade, current_time)
        )

    # 插入默认课程
    courses = [
        ('CS001', '数据结构', 'DS2023'),
        ('CS002', '算法设计', 'ALG2023'),
        ('CS003', 'Web开发', 'WEB2023'),
    ]

    for course_id, name, code in courses:
        cursor.execute(
            "INSERT OR IGNORE INTO courses (id, name, code, description, created_at) VALUES (?, ?, ?, ?, ?)",
            (course_id, name, code, f'{name}课程', current_time)
        )

    # 插入默认教师（密码：123456）
    default_password = hashlib.md5("123456".encode()).hexdigest()
    teachers = [
        ('T001', '张老师', '<EMAIL>', '13800138001'),
        ('T002', '李老师', '<EMAIL>', '13800138002'),
        ('T003', '王老师', '<EMAIL>', '13800138003'),
    ]

    for teacher_id, name, email, phone in teachers:
        cursor.execute(
            "INSERT OR IGNORE INTO teachers (teacher_id, name, password, email, phone, created_at) VALUES (?, ?, ?, ?, ?, ?)",
            (teacher_id, name, default_password, email, phone, current_time)
        )

    # 插入默认学生（密码：123456）
    students = [
        ('S001', '张三', '男', 'CL001'),
        ('S002', '李四', '女', 'CL001'),
        ('S003', '王五', '男', 'CL001'),
        ('S004', '赵六', '女', 'CL001'),
        ('S005', '钱七', '男', 'CL001'),
        ('S006', '孙八', '女', 'CL001'),
        ('S007', '周九', '男', 'CL001'),
        ('S008', '吴十', '女', 'CL001'),
        ('S009', '郑一', '男', 'CL002'),
        ('S010', '王二', '女', 'CL002'),
        ('S011', '李三', '男', 'CL002'),
        ('S012', '张四', '女', 'CL002'),
        ('S013', '刘五', '男', 'CL002'),
        ('S014', '陈六', '女', 'CL002'),
        ('S015', '杨七', '男', 'CL002'),
        ('S016', '黄八', '女', 'CL002'),
    ]

    for student_id, name, gender, class_id in students:
        cursor.execute(
            "INSERT OR IGNORE INTO students (student_id, name, password, gender, class_id, created_at) VALUES (?, ?, ?, ?, ?, ?)",
            (student_id, name, default_password, gender, class_id, current_time)
        )

    # 插入默认课程安排
    course_schedules = [
        ('CS_001', 'CS001', 'T001', 'CR001', 'CL001', 1, '08:00', '10:00', 'scheduled', '数据结构课程'),
        ('CS_002', 'CS002', 'T002', 'CR002', 'CL001', 3, '10:00', '12:00', 'scheduled', '算法设计课程'),
        ('CS_003', 'CS003', 'T003', 'CR003', 'CL002', 2, '14:00', '16:00', 'scheduled', 'Web开发课程'),
        ('CS_004', 'CS001', 'T001', 'CR001', 'CL002', 4, '08:00', '10:00', 'scheduled', '数据结构课程'),
    ]

    for schedule_id, course_id, teacher_id, classroom_id, class_id, day_of_week, start_time, end_time, status, description in course_schedules:
        cursor.execute(
            "INSERT OR IGNORE INTO course_schedules (id, course_id, teacher_id, classroom_id, class_id, day_of_week, start_time, end_time, status, description, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (schedule_id, course_id, teacher_id, classroom_id, class_id, day_of_week, start_time, end_time, status, description, current_time)
        )

    # 插入测试习题数据
    test_exercises = [
        (str(uuid.uuid4()), 'T001', 'single', '以下哪个是Python的基本数据类型？',
         json.dumps(['整数', '字符串', '列表', '字典'], ensure_ascii=False), '整数', 'easy'),
        (str(uuid.uuid4()), 'T001', 'multiple', '以下哪些是面向对象编程的特征？',
         json.dumps(['封装', '继承', '多态', '抽象'], ensure_ascii=False), '封装,继承,多态', 'medium'),
        (str(uuid.uuid4()), 'T001', 'judge', 'Python是一种解释型语言。',
         None, '正确', 'easy'),
        (str(uuid.uuid4()), 'T001', 'fill', '在Python中，用于定义函数的关键字是____。',
         None, 'def', 'easy'),
        (str(uuid.uuid4()), 'T001', 'subjective', '请简述Python中列表和元组的区别。',
         None, '列表是可变的，元组是不可变的；列表用[]表示，元组用()表示。', 'medium'),
        (str(uuid.uuid4()), 'T001', 'single', '在数据结构中，栈的特点是什么？',
         json.dumps(['先进先出', '后进先出', '随机访问', '顺序访问'], ensure_ascii=False), '后进先出', 'medium'),
        (str(uuid.uuid4()), 'T001', 'multiple', '以下哪些是常见的排序算法？',
         json.dumps(['冒泡排序', '快速排序', '归并排序', '选择排序'], ensure_ascii=False), '冒泡排序,快速排序,归并排序,选择排序', 'hard'),
        (str(uuid.uuid4()), 'T001', 'judge', '二叉树的遍历只有前序、中序、后序三种方式。',
         None, '错误', 'medium'),
        (str(uuid.uuid4()), 'T001', 'fill', '时间复杂度为O(n²)的排序算法有冒泡排序和____排序。',
         None, '选择', 'hard'),
        (str(uuid.uuid4()), 'T001', 'subjective', '请解释什么是算法的时间复杂度和空间复杂度。',
         None, '时间复杂度是算法执行时间与输入规模的关系；空间复杂度是算法占用存储空间与输入规模的关系。', 'hard'),
    ]

    for exercise_id, teacher_id, ex_type, question, options, answer, difficulty in test_exercises:
        cursor.execute(
            "INSERT OR IGNORE INTO exercises (id, teacher_id, type, question, options, answer, difficulty, folder_path, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            (exercise_id, teacher_id, ex_type, question, options, answer, difficulty, '/', current_time, current_time)
        )

    # 插入测试试卷数据
    paper_questions = [ex[0] for ex in test_exercises[:5]]
    paper_data = {
        "questions": paper_questions,
        "total_score": 100
    }
    cursor.execute(
        "INSERT OR IGNORE INTO papers (id, title, description, teacher_id, created_at, data) VALUES (?, ?, ?, ?, ?, ?)",
        (str(uuid.uuid4()), "T001老师的测试试卷", "一份包含5道题的测试试卷", "T001", current_time, json.dumps(paper_data))
    )
