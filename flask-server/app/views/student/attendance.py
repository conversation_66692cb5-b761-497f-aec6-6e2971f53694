from flask import jsonify, session, request
from app.models.database import get_db
from app.utils.decorators import student_required
from datetime import datetime
import uuid

def register_routes(bp):
    """注册签到相关路由"""
    @bp.route('/sign_in_ajax', methods=['POST'])
    @student_required
    def sign_in_ajax():
        """学生通过AJAX进行课堂签到"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            cursor.execute("SELECT student_id, name, class_id FROM students WHERE student_id = ?", (session.get('student_id'),))
            student = cursor.fetchone()

            if not student:
                conn.close()
                return jsonify({"code": 400, "message": "学生信息不存在"})

            # 查找当前正在进行的课程
            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE class_id = ? AND status = 'in_progress'
                LIMIT 1
            """, (student['class_id'],))
            active_course = cursor.fetchone()

            if not active_course:
                conn.close()
                return jsonify({"code": 404, "message": "当前没有正在进行的课程"})

            course_schedule_id = active_course['id']

            # 检查是否已签到
            cursor.execute("""
                SELECT id FROM class_attendance
                WHERE course_schedule_id = ? AND student_id = ? AND status = 'present' AND signin_time != ''
            """, (course_schedule_id, student['student_id']))
            if cursor.fetchone():
                conn.close()
                return jsonify({"code": 409, "message": "您已经签到过了"})

            # 更新或插入签到记录
            cursor.execute("SELECT id FROM class_attendance WHERE course_schedule_id = ? AND student_id = ?", (course_schedule_id, student['student_id']))
            existing_record = cursor.fetchone()

            if existing_record:
                cursor.execute("""
                    UPDATE class_attendance
                    SET signin_time = ?, status = ?
                    WHERE course_schedule_id = ? AND student_id = ?
                """, (datetime.now().isoformat(), 'present', course_schedule_id, student['student_id']))
            else:
                attendance_id = str(uuid.uuid4())
                cursor.execute("""
                    INSERT INTO class_attendance (id, course_schedule_id, student_id, signin_time, status, remark, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (attendance_id, course_schedule_id, student['student_id'], datetime.now().isoformat(), 'present', '', datetime.now().isoformat()))

            conn.commit()
            conn.close()

            return jsonify({"code": 200, "message": "签到成功"})
        except Exception as e:
            return jsonify({"code": 500, "message": f"服务器内部错误: {e}"})

    @bp.route('/qr_sign_in', methods=['POST'])
    @student_required
    def qr_sign_in():
        """学生通过扫码进行课堂签到"""
        try:
            course_schedule_id = request.form.get('course_schedule_id')

            if not course_schedule_id:
                return jsonify({"code": 400, "message": "缺少课程ID"})

            conn = get_db()
            cursor = conn.cursor()

            cursor.execute("SELECT student_id, name, class_id FROM students WHERE student_id = ?", (session.get('student_id'),))
            student = cursor.fetchone()

            if not student:
                conn.close()
                return jsonify({"code": 400, "message": "学生信息不存在"})

            # 验证课程是否存在且正在进行
            cursor.execute("""
                SELECT id, class_id, status FROM course_schedules
                WHERE id = ? AND status = 'in_progress'
            """, (course_schedule_id,))
            course = cursor.fetchone()

            if not course:
                conn.close()
                return jsonify({"code": 404, "message": "课程不存在或未开始"})

            # 验证学生是否属于该课程的班级
            if student['class_id'] != course['class_id']:
                conn.close()
                return jsonify({"code": 403, "message": "您不属于该课程的班级"})

            # 检查是否已经签到
            cursor.execute("""
                SELECT id FROM class_attendance
                WHERE course_schedule_id = ? AND student_id = ? AND status = 'present'
            """, (course_schedule_id, student['student_id']))

            if cursor.fetchone():
                conn.close()
                return jsonify({"code": 400, "message": "您已经签到过了"})

            # 更新或插入签到记录
            cursor.execute("SELECT id FROM class_attendance WHERE course_schedule_id = ? AND student_id = ?", (course_schedule_id, student['student_id']))
            existing_record = cursor.fetchone()

            if existing_record:
                cursor.execute("""
                    UPDATE class_attendance
                    SET signin_time = ?, status = ?
                    WHERE course_schedule_id = ? AND student_id = ?
                """, (datetime.now().isoformat(), 'present', course_schedule_id, student['student_id']))
            else:
                attendance_id = str(uuid.uuid4())
                cursor.execute("""
                    INSERT INTO class_attendance (id, course_schedule_id, student_id, signin_time, status, remark, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (attendance_id, course_schedule_id, student['student_id'], datetime.now().isoformat(), 'present', '扫码签到', datetime.now().isoformat()))

            conn.commit()
            conn.close()

            return jsonify({"code": 200, "message": "扫码签到成功"})

        except Exception as e:
            return jsonify({"code": 500, "message": f"签到失败: {str(e)}"})
