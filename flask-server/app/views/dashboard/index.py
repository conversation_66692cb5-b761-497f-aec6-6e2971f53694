"""
仪表盘课程相关路由
"""
from flask import render_template, jsonify, session, redirect, url_for, request
from app.models.database import get_db
from app.utils.decorators import teacher_required
from datetime import datetime
from . import dashboard_bp

@dashboard_bp.route('/')
@teacher_required
def index():
    """课程班级页面"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 获取当前教师的所有课程安排
        cursor.execute("""
            SELECT cs.id, cs.course_id, cs.class_id, cs.classroom_id, cs.day_of_week,
                   cs.start_time, cs.end_time, cs.status, cs.start_datetime, cs.end_datetime,
                   c.name as course_name, c.code as course_code,
                   cl.name as class_name,
                   cr.name as classroom_name
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classes cl ON cs.class_id = cl.id
            JOIN classrooms cr ON cs.classroom_id = cr.id
            WHERE cs.teacher_id = ?
            ORDER BY cs.day_of_week, cs.start_time
        """, (session.get('teacher_id'),))

        courses_raw = cursor.fetchall()
        conn.close()

        # 转换星期数字为中文
        day_names = {
            1: '周一', 2: '周二', 3: '周三', 4: '周四',
            5: '周五', 6: '周六', 7: '周日'
        }

        courses = []
        for course in courses_raw:
            course_dict = dict(course)
            
            # 转换 end_datetime 字符串为 datetime 对象
            if course_dict.get('end_datetime') and isinstance(course_dict['end_datetime'], str):
                try:
                    # 尝试解析 ISO 格式的日期时间字符串
                    course_dict['end_datetime'] = datetime.fromisoformat(course_dict['end_datetime'])
                except ValueError:
                    # 如果解析失败，可以尝试其他格式或设置为 None
                    course_dict['end_datetime'] = None

            # 如果day_of_week是数字，进行转换；如果已经是字符串，保持原样
            if isinstance(course['day_of_week'], int):
                course_dict['day_of_week'] = day_names.get(course['day_of_week'], f'周{course["day_of_week"]}')
            else:
                # 如果已经是字符串，保持原样或进行简单清理
                day_str = str(course['day_of_week'])
                if day_str.startswith('周'):
                    course_dict['day_of_week'] = day_str
                else:
                    course_dict['day_of_week'] = day_str
            courses.append(course_dict)

        return render_template("dashboard/index.html",
                             current_page="index",
                             courses=courses)

    except Exception as e:
        return render_template("dashboard/index.html",
                             current_page="index",
                             courses=[],
                             error=str(e))

@dashboard_bp.route('/start_class/<course_schedule_id>', methods=['POST'])
@teacher_required
def start_class(course_schedule_id):
    """开始上课"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 验证课程安排是否属于当前教师
        cursor.execute("""
            SELECT id, status FROM course_schedules
            WHERE id = ? AND teacher_id = ?
        """, (course_schedule_id, session.get('teacher_id')))

        course = cursor.fetchone()
        if not course:
            conn.close()
            return jsonify({"status": "error", "message": "课程不存在或无权限"}), 403

        if course['status'] == 'in_progress':
            conn.close()
            return jsonify({"status": "error", "message": "课程已经在进行中"}), 400

        # 结束其他正在进行的课程
        cursor.execute("""
            UPDATE course_schedules
            SET status = 'completed', end_datetime = ?
            WHERE teacher_id = ? AND status = 'in_progress'
        """, (datetime.now().isoformat(), session.get('teacher_id')))

        # 开始当前课程
        cursor.execute("""
            UPDATE course_schedules
            SET status = 'in_progress', start_datetime = ?
            WHERE id = ?
        """, (datetime.now().isoformat(), course_schedule_id))

        # 设置当前激活的课程ID到session中
        session['course_schedule_id'] = course_schedule_id

        conn.commit()
        conn.close()

        return jsonify({"status": "success", "message": "课程已开始"})

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@dashboard_bp.route('/enter_class/<course_schedule_id>')
@teacher_required
def enter_class(course_schedule_id):
    """进入上课系统"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 验证课程安排是否属于当前教师
        cursor.execute("""
            SELECT id, status FROM course_schedules
            WHERE id = ? AND teacher_id = ?
        """, (course_schedule_id, session.get('teacher_id')))

        course = cursor.fetchone()
        if not course:
            conn.close()
            return redirect(url_for('index'))

        # 设置当前选中的课堂
        session['current_class_id'] = course_schedule_id
        conn.close()

        # 重定向到上课系统
        return redirect(url_for('teacher.index'))

    except Exception as e:
        return redirect(url_for('index'))





@dashboard_bp.route('/verify_client_login', methods=['POST'])
@teacher_required
def verify_client_login():
    """验证客户端登录二维码"""
    try:
        teacher_id = request.form.get('teacher_id')
        token = request.form.get('token')
        timestamp = request.form.get('timestamp')

        if not teacher_id or not token or not timestamp:
            return jsonify({"status": "error", "message": "缺少必要参数"}), 400

        # 验证时间戳（5分钟有效期）
        import time
        current_time = int(time.time())
        qr_time = int(timestamp)

        if current_time - qr_time > 300:  # 5分钟
            return jsonify({"status": "error", "message": "二维码已过期"}), 400

        # 验证教师ID和token（放宽验证条件，因为这是扫码验证客户端登录）
        current_teacher_id = session.get('teacher_id')
        current_token = session.get('token')

        # 只验证教师ID，token可能不同（客户端和网页端的token可能不同）
        if teacher_id != current_teacher_id:
            return jsonify({"status": "error", "message": "教师ID不匹配"}), 403

        # 验证成功，可以在这里添加其他逻辑
        # 比如记录客户端连接状态等

        return jsonify({
            "status": "success",
            "message": "客户端登录验证成功",
            "teacher_id": teacher_id
        })

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500
