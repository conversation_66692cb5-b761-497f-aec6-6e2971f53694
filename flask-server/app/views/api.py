from flask import Blueprint, jsonify, g, request
from app import socketio, active_timers
from app.models.database import get_db
from app.utils.decorators import api_login_required, teacher_required
from app.services.file_service import FileService
from app.services.homework_service import HomeworkService
from datetime import datetime
import json
import uuid
import time

# 导入 group_clients 以便 API 可以访问在线小组列表
from app.socket_events import group_clients

api_bp = Blueprint('api', __name__, url_prefix='/api')

@api_bp.route('/teacher/papers', methods=['GET'])
@api_login_required
def get_teacher_papers():
    """获取当前教师的试卷库列表"""
    teacher_id = g.teacher_id
    papers = HomeworkService.get_papers_by_teacher(teacher_id)
    return jsonify(success=True, papers=papers)

@api_bp.route('/teacher/interactive/start', methods=['POST'])
@api_login_required
def start_interactive_session():
    """
    教师端发起一个互动答题
    需要 paper_id, title, course_schedule_id
    """
    data = request.get_json()
    if not data:
        return jsonify(success=False, message="无效的请求"), 400

    paper_id = data.get('paper_id')
    title = data.get('title')
    course_schedule_id = data.get('course_schedule_id')
    teacher_id = g.teacher_id

    if not all([paper_id, title, course_schedule_id]):
        return jsonify(success=False, message="缺少必要参数 (paper_id, title, course_schedule_id)"), 400

    service = HomeworkService()
    success, message, homework_id = service.create_interactive_session(
        teacher_id=teacher_id,
        course_id=course_schedule_id,
        paper_id=paper_id,
        title=title
    )

    if success:
        # 可以在这里广播一个事件，通知学生端有新的互动答题
        socketio.emit('new_interactive_quiz', {'homework_id': homework_id, 'title': title}, room=f'class_{course_schedule_id}')
        return jsonify(success=True, message=message, homework_id=homework_id)
    else:
        return jsonify(success=False, message=message), 500

@api_bp.route('/exercises/<exercise_id>', methods=['GET'])
@api_login_required
def get_exercise_details(exercise_id):
    """获取单个习题的详细信息（不含答案）"""
    try:
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute("SELECT id, type, question, options, difficulty FROM exercises WHERE id = ?", (exercise_id,))
        exercise = cursor.fetchone()
        conn.close()
        if exercise:
            exercise_dict = dict(exercise)
            # 解析选项
            if exercise_dict.get('options') and isinstance(exercise_dict['options'], str):
                exercise_dict['options'] = json.loads(exercise_dict['options'])
            return jsonify(success=True, exercise=exercise_dict)
        else:
            return jsonify(success=False, message="习题未找到"), 404
    except Exception as e:
        return jsonify(success=False, message=str(e)), 500

@api_bp.route('/teacher/homeworks', methods=['POST'])
@api_login_required
def api_create_homework():
    """从资源库导入试卷作为作业 (API for App)"""
    try:
        teacher_id = g.teacher_id
        data = request.get_json()
        
        paper_id = data.get('paper_id')
        title = data.get('title')
        deadline = data.get('deadline')
        description = data.get('description', '')
        course_id = data.get('course_id')

        if not all([paper_id, title, course_id, deadline]):
            return jsonify({"status": "error", "message": "缺少必要参数(paper_id, title, course_id, deadline)"}), 400

        homework_service = HomeworkService()
        success, message, homework_id = homework_service.create_homework_from_paper(
            teacher_id, course_id, paper_id, title, deadline, description
        )

        if success:
            return jsonify({"status": "success", "message": message, "homework_id": homework_id}), 201
        else:
            # 根据错误类型返回不同状态码
            if "无权限" in message or "不存在" in message:
                return jsonify({"status": "error", "message": message}), 404
            else:
                return jsonify({"status": "error", "message": message}), 500

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@api_bp.route('/teacher/homeworks/<course_schedule_id>', methods=['GET'])
@api_login_required
def api_get_teacher_homeworks(course_schedule_id):
    """获取指定课程的作业列表 (API for App)"""
    try:
        teacher_id = g.teacher_id
        homework_service = HomeworkService()
        
        success, message, homeworks = homework_service.get_homework_list_for_teacher(teacher_id, course_schedule_id)
        
        if success:
            return jsonify({"status": "success", "homeworks": homeworks})
        else:
            return jsonify({"status": "error", "message": message}), 500
            
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@api_bp.route('/teacher/homeworks/<homework_id>/publish', methods=['POST'])
@api_login_required
def api_publish_homework(homework_id):
    """发布作业 (API for App)"""
    try:
        teacher_id = g.teacher_id
        homework_service = HomeworkService()
        success, message = homework_service.publish_homework(teacher_id, homework_id)

        if success:
            return jsonify({"status": "success", "message": message})
        else:
            if "无权限" in message or "不存在" in message:
                return jsonify({"status": "error", "message": message}), 404
            else:
                return jsonify({"status": "error", "message": message}), 500
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@api_bp.route('/teacher/homeworks/<homework_id>', methods=['DELETE'])
@api_login_required
def api_delete_homework(homework_id):
    """删除作业 (API for App)"""
    try:
        teacher_id = g.teacher_id
        homework_service = HomeworkService()
        success, message = homework_service.delete_homework(teacher_id, homework_id)

        if success:
            return jsonify({"status": "success", "message": message})
        else:
            if "无权限" in message or "不存在" in message:
                return jsonify({"status": "error", "message": message}), 404
            else:
                return jsonify({"status": "error", "message": message}), 500
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@api_bp.route('/teacher/papers', methods=['GET'])
@api_login_required
def api_get_teacher_papers():
    """获取教师的试卷库列表 (API for App)"""
    try:
        teacher_id = g.teacher_id
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute("SELECT id, title FROM papers WHERE teacher_id = ?", (teacher_id,))
        papers = [dict(row) for row in cursor.fetchall()]
        conn.close()
        return jsonify({"status": "success", "papers": papers})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

# 学生端答题相关API
@api_bp.route('/student/homework/<homework_id>', methods=['GET'])
@api_login_required
def get_homework_for_student(homework_id):
    """学生端获取作业/互动答题详情"""
    try:
        student_id = g.student_id
        conn = get_db()
        cursor = conn.cursor()
        
        # 1. 验证作业存在且已发布
        cursor.execute("""
            SELECT h.id, h.title, h.data, h.status, h.type, h.deadline
            FROM homework h
            JOIN course_schedules cs ON h.course_schedule_id = cs.id
            JOIN students s ON s.class_id = cs.class_id
            WHERE h.id = ? AND s.student_id = ? AND h.status = 'published'
        """, (homework_id, student_id))
        
        homework = cursor.fetchone()
        if not homework:
            conn.close()
            return jsonify({"status": "error", "message": "作业不存在或无权限访问"}), 404
        
        # 2. 解析作业数据获取题目详情
        homework_data = json.loads(homework['data'])
        if isinstance(homework_data, dict):
            questions = homework_data.get('questions', [])
        elif isinstance(homework_data, list):
            questions = homework_data
        else:
            questions = []
        
        # 3. 获取题目详情
        question_details = []
        for question in questions:
            if isinstance(question, dict):
                # 如果已经是完整的题目数据，统一字段名
                if 'desc' in question and 'content' not in question:
                    question['content'] = question['desc']
                # 处理判断题的选项
                if question.get('type') == 'judge' and not question.get('options'):
                    question['options'] = ['正确', '错误']
                question_details.append(question)
            else:
                # 如果是题目ID，需要从exercises表中获取
                cursor.execute("""
                    SELECT id, type, question, options, difficulty
                    FROM exercises WHERE id = ?
                """, (question,))
                exercise = cursor.fetchone()
                if exercise:
                    exercise_dict = dict(exercise)
                    # 重命名字段以匹配前端
                    exercise_dict['content'] = exercise_dict.pop('question')
                    # 解析选项
                    if exercise_dict.get('options'):
                        try:
                            exercise_dict['options'] = json.loads(exercise_dict['options'])
                        except:
                            exercise_dict['options'] = []
                    else:
                        exercise_dict['options'] = []
                    
                    # 处理判断题的选项
                    if exercise_dict.get('type') == 'judge' and not exercise_dict['options']:
                        exercise_dict['options'] = ['正确', '错误']
                    question_details.append(exercise_dict)
        
        conn.close()
        
        result = {
            "status": "success",
            "homework": {
                "id": homework['id'],
                "title": homework['title'],
                "type": homework['type'],
                "deadline": homework['deadline'],
                "questions": question_details
            }
        }
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@api_bp.route('/student/homework/<homework_id>/submissions', methods=['GET'])
@api_login_required
def get_student_submissions(homework_id):
    """获取学生的答题提交记录"""
    try:
        student_id = g.student_id
        conn = get_db()
        cursor = conn.cursor()
        
        # 获取学生的提交记录
        cursor.execute("""
            SELECT question_id, answer, submitted_at
            FROM homework_submissions
            WHERE homework_id = ? AND student_id = ?
        """, (homework_id, student_id))
        
        submissions = cursor.fetchall()
        conn.close()
        
        # 转换为字典格式
        submission_dict = {}
        for sub in submissions:
            submission_dict[sub['question_id']] = {
                'answer': sub['answer'],
                'submitted_at': sub['submitted_at']
            }
        
        return jsonify({
            "status": "success",
            "submissions": submission_dict
        })
        
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@api_bp.route('/student/homework/<homework_id>/submit', methods=['POST'])
@api_login_required
def submit_homework_answer(homework_id):
    """学生端提交答题，并进行实时批改"""
    conn = get_db()
    cursor = conn.cursor()
    try:
        student_id = g.student_id
        data = request.get_json()
        
        question_id = data.get('question_id')
        answer = data.get('answer')
        
        if not question_id or answer is None:
            return jsonify({"status": "error", "message": "缺少必要参数"}), 400
        
        # 1. 验证作业权限
        cursor.execute("""
            SELECT h.id, h.type, h.status, cs.class_id, cs.id as course_schedule_id
            FROM homework h
            JOIN course_schedules cs ON h.course_schedule_id = cs.id
            JOIN students s ON s.class_id = cs.class_id
            WHERE h.id = ? AND s.student_id = ? AND h.status = 'published'
        """, (homework_id, student_id))
        
        homework = cursor.fetchone()
        if not homework:
            return jsonify({"status": "error", "message": "作业不存在或无权限访问"}), 404
        
        # 2. 获取题目正确答案并批改
        cursor.execute("SELECT answer, type FROM exercises WHERE id = ?", (question_id,))
        exercise = cursor.fetchone()
        is_correct = False
        if exercise:
            correct_answer = exercise['answer']
            # 对不同题型进行批改
            if exercise['type'] == 'multiple':
                # 将答案转换为集合进行比较，忽略顺序
                correct_set = set(str(ans).strip() for ans in str(correct_answer).split(','))
                student_set = set(str(ans).strip() for ans in answer) if isinstance(answer, list) else {str(answer).strip()}
                if correct_set == student_set:
                    is_correct = True
            elif str(correct_answer).strip() == str(answer).strip():
                is_correct = True
        
        # 3. 存储或更新提交记录到 quiz_results
        current_time = datetime.now().isoformat()
        cursor.execute(
            "SELECT id FROM quiz_results WHERE homework_id = ? AND student_id = ? AND question_id = ?",
            (homework_id, student_id, question_id)
        )
        existing_result = cursor.fetchone()

        if existing_result:
            cursor.execute(
                "UPDATE quiz_results SET answer = ?, is_correct = ?, submitted_at = ? WHERE id = ?",
                (json.dumps(answer), is_correct, current_time, existing_result['id'])
            )
        else:
            result_id = str(uuid.uuid4())
            cursor.execute(
                "INSERT INTO quiz_results (id, homework_id, student_id, question_id, answer, is_correct, submitted_at) VALUES (?, ?, ?, ?, ?, ?, ?)",
                (result_id, homework_id, student_id, question_id, json.dumps(answer), is_correct, current_time)
            )

        conn.commit()
        
        # 4. 如果是互动答题，通过WebSocket广播答题数据和批改结果
        if homework['type'] == 'interactive':
            from app import socketio
            socketio.emit('student_answer', {
                'homework_id': homework_id,
                'question_id': question_id,
                'student_id': student_id,
                'answer': answer,
                'is_correct': is_correct
            }, room=f'course_{homework["course_schedule_id"]}')
        
        return jsonify({"status": "success", "message": "答题提交成功", "is_correct": is_correct})
        
    except Exception as e:
        if conn:
            conn.rollback()
        return jsonify({"status": "error", "message": str(e)}), 500
    finally:
        if conn:
            conn.close()

@api_bp.route('/teacher/homework/<homework_id>', methods=['GET'])
@api_login_required
def get_homework_details(homework_id):
    """教师端获取作业详情（包含题目信息）"""
    try:
        teacher_id = g.teacher_id
        conn = get_db()
        cursor = conn.cursor()
        
        # 1. 验证作业权限
        cursor.execute("""
            SELECT id, title, description, data, status, type, deadline, question_count, total_students
            FROM homework WHERE id = ? AND teacher_id = ?
        """, (homework_id, teacher_id))
        
        homework = cursor.fetchone()
        if not homework:
            conn.close()
            return jsonify({"status": "error", "message": "作业不存在或无权限"}), 404
        
        # 2. 解析作业数据获取题目详情
        homework_data = json.loads(homework['data'])
        if isinstance(homework_data, dict):
            questions = homework_data.get('questions', [])
        elif isinstance(homework_data, list):
            questions = homework_data
        else:
            questions = []
        
        # 3. 获取题目详情
        question_details = []
        for question in questions:
            if isinstance(question, dict):
                # 如果已经是完整的题目数据，统一字段名
                if 'desc' in question and 'content' not in question:
                    question['content'] = question['desc']
                # 处理判断题的选项
                if question.get('type') == 'judge' and not question.get('options'):
                    question['options'] = ['正确', '错误']
                question_details.append(question)
            else:
                # 如果是题目ID，需要从exercises表中获取
                cursor.execute("""
                    SELECT id, type, question, options, difficulty
                    FROM exercises WHERE id = ?
                """, (question,))
                exercise = cursor.fetchone()
                if exercise:
                    exercise_dict = dict(exercise)
                    # 重命名字段以匹配前端
                    exercise_dict['content'] = exercise_dict.pop('question')
                    # 解析选项
                    if exercise_dict.get('options'):
                        try:
                            exercise_dict['options'] = json.loads(exercise_dict['options'])
                        except:
                            exercise_dict['options'] = []
                    else:
                        exercise_dict['options'] = []
                    
                    # 处理判断题的选项
                    if exercise_dict.get('type') == 'judge' and not exercise_dict['options']:
                        exercise_dict['options'] = ['正确', '错误']
                    question_details.append(exercise_dict)
        
        conn.close()
        
        result = {
            "status": "success",
            "details": {
                "id": homework['id'],
                "title": homework['title'],
                "description": homework['description'],
                "status": homework['status'],
                "type": homework['type'],
                "deadline": homework['deadline'],
                "question_count": homework['question_count'],
                "total_students": homework['total_students'],
                "questions": question_details
            }
        }
        
        return jsonify(result)
        
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@api_bp.route('/teacher/interactive/<homework_id>/end', methods=['POST'])
@api_login_required
def end_interactive_session(homework_id):
    """教师端结束互动答题"""
    try:
        teacher_id = g.teacher_id
        conn = get_db()
        cursor = conn.cursor()
        
        # 1. 验证作业权限
        cursor.execute("""
            SELECT h.id, h.type, cs.class_id, cs.id as course_schedule_id
            FROM homework h
            JOIN course_schedules cs ON h.course_schedule_id = cs.id
            WHERE h.id = ? AND h.teacher_id = ? AND h.type = 'interactive'
        """, (homework_id, teacher_id))
        
        homework = cursor.fetchone()
        if not homework:
            conn.close()
            return jsonify({"status": "error", "message": "作业不存在或无权限"}), 404
        
        # 2. 更新作业状态为已结束
        cursor.execute("""
            UPDATE homework SET status = 'finished'
            WHERE id = ?
        """, (homework_id,))
        
        conn.commit()
        conn.close()
        
        # 3. 通过WebSocket通知学生端答题已结束
        from app import socketio
        socketio.emit('quiz_ended', {
            'homework_id': homework_id,
            'message': '教师已结束本次互动答题'
        }, room=f'course_{homework["course_schedule_id"]}')
        
        return jsonify({"status": "success", "message": "互动答题已结束"})
        
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@api_bp.route('/files/upload', methods=['POST'])
@api_login_required
def api_upload_files():
    """专用于客户端的文件上传API"""
    try:
        if 'files' not in request.files:
            return jsonify({"status": "error", "message": "没有选择文件"}), 400

        files = request.files.getlist('files')
        if not files or all(file.filename == '' for file in files):
            return jsonify({"status": "error", "message": "没有选择文件"}), 400

        teacher_id = g.teacher_id
        # 对于客户端上传，我们默认存入根目录 (folder_id=None)
        folder_id = None 

        file_service = FileService()
        uploaded_files = []
        failed_files = []

        for file in files:
            if file.filename == '':
                continue

            success, message, file_id = file_service.upload_file(teacher_id, file, folder_id)
            if success:
                # 获取文件信息用于广播
                success_info, msg_info, file_info = file_service.get_file_info(teacher_id, file_id)
                if success_info:
                    uploaded_files.append(file_info)
                else:
                    failed_files.append(f"{file.filename}: {msg_info}")
            else:
                failed_files.append(f"{file.filename}: {message}")

        if uploaded_files:
            # 广播新文件事件给所有客户端
            for uploaded_file in uploaded_files:
                socketio.emit('new_file_available', {
                    'filename': uploaded_file['original_filename'],
                    'url': f"/uploads/{uploaded_file['file_path']}",
                    'file_type': uploaded_file['mime_type'],
                    'file_size': uploaded_file['file_size']
                })

            response_message = f"成功上传 {len(uploaded_files)} 个文件"
            if failed_files:
                response_message += f"，{len(failed_files)} 个文件上传失败"

            return jsonify({
                "status": "success",
                "message": response_message,
                "uploaded_files": [f['original_filename'] for f in uploaded_files],
                "failed_files": failed_files
            })
        else:
            return jsonify({
                "status": "error",
                "message": "所有文件上传失败",
                "failed_files": failed_files
            }), 400

    except Exception as e:
        return jsonify({"status": "error", "message": f"上传失败: {str(e)}"}), 500

@api_bp.route('/teacher/courses', methods=['GET'])
@api_login_required
def get_teacher_courses():
    """获取当前教师的所有课程安排 (API for App)"""
    try:
        teacher_id = g.teacher_id  # 从g对象获取认证过的用户ID
        conn = get_db()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                   cl.name as classroom_name, cls.name as class_name,
                   cs.day_of_week, cs.start_time, cs.end_time, cs.status
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            WHERE cs.teacher_id = ? AND cs.status != 'completed'
            ORDER BY cs.day_of_week, cs.start_time
        """, (teacher_id,))

        courses = [dict(row) for row in cursor.fetchall()]
        conn.close()

        return jsonify({"status": "success", "courses": courses})

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@api_bp.route('/student/homeworks', methods=['GET'])
@api_login_required
def api_get_student_homeworks():
    """获取当前学生的所有作业列表 (API for App/Web)"""
    try:
        student_id = g.student_id
        conn = get_db()
        cursor = conn.cursor()

        # 获取该学生所在班级的所有已发布作业，并检查该生是否已提交
        cursor.execute("""
            SELECT h.id, h.title, h.description, h.created_at, h.deadline, c.name as course_name,
                   CASE WHEN hr.id IS NOT NULL THEN 1 ELSE 0 END as is_submitted,
                   hr.score, hr.submitted_at
            FROM homework h
            JOIN course_schedules cs ON h.course_schedule_id = cs.id
            JOIN courses c ON cs.course_id = c.id
            JOIN students s ON s.class_id = cs.class_id
            LEFT JOIN homework_results hr ON h.id = hr.homework_id AND hr.student_id = s.student_id
            WHERE s.student_id = ? AND h.status = 'published'
            ORDER BY h.created_at DESC
        """, (student_id,))

        homeworks = [dict(row) for row in cursor.fetchall()]
        conn.close()

        return jsonify({"status": "success", "homeworks": homeworks})

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@api_bp.route('/teacher/homework/<homework_id>', methods=['GET'])
@api_login_required
def api_get_homework_details(homework_id):
    """获取单个作业的详细信息 (API for App)"""
    try:
        teacher_id = g.teacher_id
        homework_service = HomeworkService()
        success, message, details = homework_service.get_homework_details(teacher_id, homework_id)

        if success:
            return jsonify({"status": "success", "details": details})
        else:
            if "无权限" in message or "不存在" in message:
                return jsonify({"status": "error", "message": message}), 404
            else:
                return jsonify({"status": "error", "message": message}), 500
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@api_bp.route('/teacher/homeworks/<homework_id>/submissions', methods=['GET'])
@api_login_required
def api_get_homework_submissions(homework_id):
    """获取作业的学生提交列表 (API for App)"""
    try:
        teacher_id = g.teacher_id
        homework_service = HomeworkService()
        success, message, submissions = homework_service.get_homework_submissions(teacher_id, homework_id)

        if success:
            return jsonify({"status": "success", "submissions": submissions})
        else:
            if "无权限" in message or "不存在" in message:
                return jsonify({"status": "error", "message": message}), 404
            else:
                return jsonify({"status": "error", "message": message}), 500
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@api_bp.route('/teacher/class/<course_schedule_id>/details', methods=['GET'])
@api_login_required
def api_get_class_details(course_schedule_id):
    """获取指定课堂的详细信息 (API for App)"""
    try:
        teacher_id = g.teacher_id
        conn = get_db()
        cursor = conn.cursor()

        # 1. 验证课程权限并获取课程基本信息
        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                   cl.name as classroom_name, cls.name as class_name,
                   cs.day_of_week, cs.start_time, cs.end_time, cs.status
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            WHERE cs.id = ? AND cs.teacher_id = ?
        """, (course_schedule_id, teacher_id))
        
        course_info = cursor.fetchone()
        if not course_info:
            conn.close()
            return jsonify({"status": "error", "message": "课程不存在或无权限访问"}), 404

        # 2. 获取学生列表及签到状态
        cursor.execute("""
            SELECT s.student_id, s.name, s.gender,
                   CASE WHEN ca.status = 'present' THEN 1 ELSE 0 END as signed_in,
                   ca.signin_time
            FROM students s
            LEFT JOIN class_attendance ca ON s.student_id = ca.student_id AND ca.course_schedule_id = ?
            WHERE s.class_id = (SELECT class_id FROM course_schedules WHERE id = ?)
            ORDER BY s.name
        """, (course_schedule_id, course_schedule_id))
        students = [dict(row) for row in cursor.fetchall()]

        # 3. 获取已发布的作业列表
        cursor.execute("""
            SELECT id, title, created_at, deadline, status
            FROM homework
            WHERE course_schedule_id = ?
            ORDER BY created_at DESC
        """, (course_schedule_id,))
        homeworks = [dict(row) for row in cursor.fetchall()]

        conn.close()

        # 组装最终结果
        class_details = {
            "course_info": dict(course_info),
            "students": students,
            "homeworks": homeworks
        }

        return jsonify({"status": "success", "details": class_details})

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@api_bp.route('/teacher/class/<course_schedule_id>/start', methods=['POST'])
@api_login_required
def api_start_class(course_schedule_id):
    """开始上课 (API for App)"""
    try:
        teacher_id = g.teacher_id
        conn = get_db()
        cursor = conn.cursor()

        # 验证课程权限
        cursor.execute("SELECT id, status FROM course_schedules WHERE id = ? AND teacher_id = ?", (course_schedule_id, teacher_id))
        course = cursor.fetchone()
        if not course:
            conn.close()
            return jsonify({"status": "error", "message": "课程不存在或无权限"}), 403

        # 结束该教师其他可能正在进行的课程
        cursor.execute("UPDATE course_schedules SET status = 'completed', end_datetime = ? WHERE teacher_id = ? AND status = 'in_progress'", (datetime.now().isoformat(), teacher_id))

        # 开始当前课程
        cursor.execute("UPDATE course_schedules SET status = 'in_progress', start_datetime = ? WHERE id = ?", (datetime.now().isoformat(), course_schedule_id))
        
        conn.commit()
        conn.close()

        # 通知所有在线的小组客户端加入该课程
        if group_clients:
            print(f"准备向 {len(group_clients)} 个小组客户端发送加入课程指令...")
            for sid, client_info in group_clients.items():
                socketio.emit('join_course_command', {'course_id': course_schedule_id}, room=sid)
                print(f"  -> 已向 {client_info.get('hostname')} ({client_info.get('ip')}) 发送指令")
        else:
            print("没有在线的小组客户端可供通知。")

        return jsonify({"status": "success", "message": "课程已开始"})

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@api_bp.route('/teacher/class/<course_schedule_id>/end', methods=['POST'])
@api_login_required
def api_end_class(course_schedule_id):
    """结束上课 (API for App)"""
    try:
        teacher_id = g.teacher_id
        conn = get_db()
        cursor = conn.cursor()

        # 验证课程权限
        cursor.execute("SELECT id, status FROM course_schedules WHERE id = ? AND teacher_id = ?", (course_schedule_id, teacher_id))
        course = cursor.fetchone()
        if not course:
            conn.close()
            return jsonify({"status": "error", "message": "课程不存在或无权限"}), 403

        if course['status'] != 'in_progress':
            conn.close()
            return jsonify({"status": "error", "message": "课程未在进行中，无法结束"}), 400

        # 结束课程
        cursor.execute("UPDATE course_schedules SET status = 'completed', end_datetime = ? WHERE id = ?", (datetime.now().isoformat(), course_schedule_id))
        
        conn.commit()
        conn.close()

        return jsonify({"status": "success", "message": "课程已结束"})

    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@api_bp.route('/teacher/class/timer/start', methods=['POST'])
@api_login_required
def api_start_class_timer():
    """开始分组计时器 (API for App)"""
    data = request.get_json()
    duration = data.get('duration') # in seconds
    course_schedule_id = data.get('course_schedule_id')

    if not all([duration, course_schedule_id]):
        return jsonify(success=False, message="缺少必要参数 (duration, course_schedule_id)"), 400
    
    try:
        # 记录计时器状态
        end_time = time.time() + int(duration)
        active_timers[course_schedule_id] = end_time

        # 向指定课程的所有客户端广播计时器事件
        socketio.emit('start_timer', {'duration': duration}, room=f'course_{course_schedule_id}')
        return jsonify(success=True, message="计时器已成功广播")
    except Exception as e:
        return jsonify(success=False, message=f"广播失败: {str(e)}"), 500

@api_bp.route('/teacher/group/set_topic', methods=['POST'])
@api_login_required
def api_set_group_topic():
    """设置分组讨论主题 (API for App)"""
    data = request.get_json()
    topic_text = data.get('topic_text')
    course_schedule_id = data.get('course_schedule_id')

    if not all([topic_text, course_schedule_id]):
        return jsonify(success=False, message="缺少必要参数 (topic_text, course_schedule_id)"), 400

    try:
        # 向指定课程的所有客户端广播新主题事件
        socketio.emit('new_group_topic', {'topic': topic_text}, room=f'course_{course_schedule_id}')
        return jsonify(success=True, message="主题已成功发送")
    except Exception as e:
        return jsonify(success=False, message=f"发送主题失败: {str(e)}"), 500

@api_bp.route('/teacher/groups/save', methods=['POST'])
@api_login_required
def api_save_groups():
    """保存教师端的分组信息到数据库"""
    data = request.get_json()
    course_schedule_id = data.get('course_schedule_id')
    groups = data.get('groups')  # 格式: [{'group_name': 'Group1', 'group_ip': '*************', 'students': ['S001', 'S002']}]

    if not all([course_schedule_id, groups]):
        return jsonify(success=False, message="缺少必要参数"), 400

    try:
        teacher_id = g.teacher_id
        conn = get_db()
        cursor = conn.cursor()

        # 验证课程权限
        cursor.execute("SELECT id FROM course_schedules WHERE id = ? AND teacher_id = ?",
                      (course_schedule_id, teacher_id))
        if not cursor.fetchone():
            conn.close()
            return jsonify(success=False, message="无权限操作此课程"), 403

        # 删除该课程的旧分组
        cursor.execute("DELETE FROM group_members WHERE group_id IN (SELECT id FROM class_groups WHERE course_schedule_id = ?)",
                      (course_schedule_id,))
        cursor.execute("DELETE FROM class_groups WHERE course_schedule_id = ?", (course_schedule_id,))

        # 创建新分组
        for group_data in groups:
            group_name = group_data.get('group_name')
            group_ip = group_data.get('group_ip')
            students = group_data.get('students', [])

            if not group_name:
                continue

            # 创建分组记录
            group_id = str(uuid.uuid4())
            group_description = f"小组IP: {group_ip}" if group_ip else ""

            cursor.execute("""
                INSERT INTO class_groups (id, course_schedule_id, group_name, group_description, created_by, created_at)
                VALUES (?, ?, ?, ?, ?, ?)
            """, (group_id, course_schedule_id, group_name, group_description, teacher_id, datetime.now().isoformat()))

            # 添加学生到分组
            for student_id in students:
                member_id = str(uuid.uuid4())
                cursor.execute("""
                    INSERT INTO group_members (id, group_id, student_id, joined_at)
                    VALUES (?, ?, ?, ?)
                """, (member_id, group_id, student_id, datetime.now().isoformat()))

        conn.commit()
        conn.close()

        # 通知所有相关客户端分组已更新
        socketio.emit('groups_updated', {'course_id': course_schedule_id}, room=f'course_{course_schedule_id}')

        return jsonify(success=True, message="分组保存成功")

    except Exception as e:
        return jsonify(success=False, message=f"保存分组失败: {str(e)}"), 500

@api_bp.route('/whiteboard/sessions', methods=['POST'])
@teacher_required
def create_whiteboard_session():
    """创建白板会话"""
    try:
        from app.services.whiteboard_service import whiteboard_service

        data = request.get_json()
        group_id = data.get('group_id')
        course_schedule_id = data.get('course_schedule_id')
        session_name = data.get('session_name', f'白板会话_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
        teacher_id = session.get('teacher_id')

        if not all([group_id, course_schedule_id, teacher_id]):
            return jsonify(success=False, message="缺少必要参数"), 400

        session_id = whiteboard_service.create_session(
            group_id, course_schedule_id, session_name, teacher_id
        )

        if session_id:
            return jsonify(success=True, session_id=session_id)
        else:
            return jsonify(success=False, message="创建会话失败"), 500

    except Exception as e:
        return jsonify(success=False, message=f"创建白板会话失败: {str(e)}"), 500

@api_bp.route('/whiteboard/sessions/<session_id>/participants', methods=['GET'])
@teacher_required
def get_session_participants(session_id):
    """获取会话参与者"""
    try:
        from app.services.whiteboard_service import whiteboard_service

        participants = whiteboard_service.get_session_participants(session_id)
        return jsonify(success=True, participants=participants)

    except Exception as e:
        return jsonify(success=False, message=f"获取参与者失败: {str(e)}"), 500

@api_bp.route('/whiteboard/sessions/<session_id>/collaboration', methods=['PUT'])
@teacher_required
def update_collaboration_status(session_id):
    """更新协作状态"""
    try:
        from app.services.whiteboard_service import whiteboard_service
        from app import socketio

        data = request.get_json()
        enabled = data.get('enabled', True)

        success = whiteboard_service.update_collaboration_status(session_id, enabled)

        if success:
            # 通知所有参与者
            socketio.emit('whiteboard_collaboration_status', {
                'session_id': session_id,
                'enabled': enabled
            }, room=f"whiteboard_{session_id}")

            return jsonify(success=True)
        else:
            return jsonify(success=False, message="更新失败"), 500

    except Exception as e:
        return jsonify(success=False, message=f"更新协作状态失败: {str(e)}"), 500

@api_bp.route('/whiteboard/sessions/<session_id>/content', methods=['GET'])
@teacher_required
def get_session_content(session_id):
    """获取会话内容"""
    try:
        from app.services.whiteboard_service import whiteboard_service

        content = whiteboard_service.get_session_content(session_id)
        if content:
            return jsonify(success=True, content=content)
        else:
            return jsonify(success=False, message="未找到内容"), 404

    except Exception as e:
        return jsonify(success=False, message=f"获取内容失败: {str(e)}"), 500

@api_bp.route('/groups/<group_id>/whiteboard', methods=['GET'])
@teacher_required
def get_group_whiteboard_session(group_id):
    """获取分组的白板会话"""
    try:
        from app.services.whiteboard_service import whiteboard_service

        # 获取当前进行中的课程
        conn = get_db()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT cs.id FROM course_schedules cs
            WHERE cs.status = 'in_progress'
            ORDER BY cs.start_datetime DESC
            LIMIT 1
        """)

        course_result = cursor.fetchone()
        if not course_result:
            conn.close()
            return jsonify(success=False, message="没有进行中的课程"), 404

        course_schedule_id = course_result['id']
        conn.close()

        session_id = whiteboard_service.get_session_by_group(group_id, course_schedule_id)

        if session_id:
            content = whiteboard_service.get_session_content(session_id)
            participants = whiteboard_service.get_session_participants(session_id)

            return jsonify(
                success=True,
                session_id=session_id,
                content=content,
                participants=participants
            )
        else:
            return jsonify(success=False, message="未找到白板会话"), 404

    except Exception as e:
        return jsonify(success=False, message=f"获取白板会话失败: {str(e)}"), 500

@api_bp.route('/teacher/groups/<course_schedule_id>', methods=['GET'])
@api_login_required
def api_get_course_groups(course_schedule_id):
    """获取指定课程的分组信息"""
    try:
        teacher_id = g.teacher_id
        conn = get_db()
        cursor = conn.cursor()

        # 验证课程权限
        cursor.execute("SELECT id FROM course_schedules WHERE id = ? AND teacher_id = ?",
                      (course_schedule_id, teacher_id))
        if not cursor.fetchone():
            conn.close()
            return jsonify(success=False, message="无权限访问此课程"), 403

        # 获取分组信息
        cursor.execute("""
            SELECT cg.id, cg.group_name, cg.group_description, cg.created_at,
                   GROUP_CONCAT(gm.student_id) as student_ids,
                   GROUP_CONCAT(s.name) as student_names
            FROM class_groups cg
            LEFT JOIN group_members gm ON cg.id = gm.group_id
            LEFT JOIN students s ON gm.student_id = s.student_id
            WHERE cg.course_schedule_id = ?
            GROUP BY cg.id, cg.group_name, cg.group_description, cg.created_at
            ORDER BY cg.created_at
        """, (course_schedule_id,))

        groups = []
        for row in cursor.fetchall():
            group_data = {
                'id': row['id'],
                'group_name': row['group_name'],
                'group_description': row['group_description'],
                'created_at': row['created_at'],
                'students': []
            }

            if row['student_ids']:
                student_ids = row['student_ids'].split(',')
                student_names = row['student_names'].split(',')
                for i, student_id in enumerate(student_ids):
                    group_data['students'].append({
                        'student_id': student_id,
                        'student_name': student_names[i] if i < len(student_names) else student_id
                    })

            groups.append(group_data)

        conn.close()
        return jsonify(success=True, groups=groups)

    except Exception as e:
        return jsonify(success=False, message=f"获取分组失败: {str(e)}"), 500

@api_bp.route('/teacher/chat/send', methods=['POST'])
@api_login_required
def api_send_chat_message():
    """教师端发送聊天消息 (API for App)"""
    data = request.get_json()
    message = data.get('message')
    course_schedule_id = data.get('course_schedule_id')
    teacher_id = g.teacher_id

    if not all([message, course_schedule_id]):
        return jsonify(success=False, message="缺少必要参数 (message, course_schedule_id)"), 400
    
    try:
        # 获取教师信息
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM teachers WHERE teacher_id = ?", (teacher_id,))
        teacher = cursor.fetchone()
        conn.close()
        
        teacher_name = teacher['name'] if teacher else f"教师{teacher_id}"
        
        # 向指定课程的所有客户端广播聊天消息
        chat_data = {
            'user': teacher_name,
            'user_type': 'teacher',
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
        
        socketio.emit('chat_message', chat_data, room=f'course_{course_schedule_id}')
        return jsonify(success=True, message="消息已成功发送")
    except Exception as e:
        return jsonify(success=False, message=f"发送消息失败: {str(e)}"), 500

@api_bp.route('/teacher/profile', methods=['GET'])
@api_login_required
def api_get_teacher_profile():
    """获取教师基本信息 (API for App)"""
    try:
        teacher_id = g.teacher_id
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute("SELECT name, email FROM teachers WHERE teacher_id = ?", (teacher_id,))
        teacher = cursor.fetchone()
        conn.close()
        
        if teacher:
            return jsonify({
                "status": "success",
                "name": teacher['name'],
                "email": teacher.get('email', ''),
                "teacher_id": teacher_id
            })
        else:
            return jsonify({"status": "error", "message": "教师信息未找到"}), 404
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@api_bp.route('/student/exam/<exam_id>', methods=['GET'])
@api_login_required
def get_exam_questions(exam_id):
    """获取指定考试的题目 (API for App)"""
    try:
        student_id = g.student_id
        conn = get_db()
        cursor = conn.cursor()

        # 1. 检查学生是否有权限参与此考试
        cursor.execute("""
            SELECT h.id
            FROM homework h
            JOIN course_schedules cs ON h.course_schedule_id = cs.id
            JOIN students s ON s.class_id = cs.class_id
            WHERE h.id = ? AND s.student_id = ? AND h.status = 'published'
        """, (exam_id, student_id))
        
        exam_access = cursor.fetchone()
        if not exam_access:
            conn.close()
            return jsonify({"status": "error", "message": "考试不存在、未发布或无权限参与"}), 403

        # 2. 获取作业（考试）的基本信息和题目ID列表
        cursor.execute("SELECT title, description, deadline, data FROM homework WHERE id = ?", (exam_id,))
        exam_info = cursor.fetchone()
        if not exam_info:
            conn.close()
            return jsonify({"status": "error", "message": "未找到考试信息"}), 404

        try:
            homework_data = json.loads(exam_info['data'])
            questions_data = homework_data.get('questions', [])
            if not isinstance(questions_data, list):
                raise ValueError("题目列表格式错误")
            
            # 从题目对象列表中提取ID
            question_ids = [q.get('id') for q in questions_data if q.get('id')]
            if not question_ids:
                 # 如果手动创建的作业没有ID，则直接使用题目数据
                 # 注意：这种情况下题目信息不包含答案
                 exam_info_dict = dict(exam_info)
                 exam_info_dict['questions'] = questions_data
                 return jsonify({"status": "success", "exam": exam_info_dict})

        except (json.JSONDecodeError, ValueError) as e:
            conn.close()
            return jsonify({"status": "error", "message": f"考试题目数据格式错误: {e}"}), 500

        # 3. 获取所有题目的详细信息（不包含答案）
        questions = []
        if question_ids:
            # 构建占位符
            placeholders = ','.join(['?'] * len(question_ids))
            cursor.execute(f"""
                SELECT id, type, question, options, difficulty, folder_path
                FROM exercises
                WHERE id IN ({placeholders})
            """, question_ids)
            
            # 为了保持题目顺序，我们需要一个映射
            questions_map = {str(row['id']): dict(row) for row in cursor.fetchall()}
            
            # 按原始ID列表的顺序重新组织题目
            for q_id in question_ids:
                if q_id in questions_map:
                    question_data = questions_map[q_id]
                    # 如果options是JSON字符串，解析它
                    if isinstance(question_data.get('options'), str):
                        try:
                            question_data['options'] = json.loads(question_data['options'])
                        except json.JSONDecodeError:
                            question_data['options'] = [] # or handle error
                    questions.append(question_data)

        conn.close()

        # 4. 组装最终结果
        result = {
            "id": exam_id,
            "title": exam_info['title'],
            "description": exam_info['description'],
            "deadline": exam_info['deadline'],
            "questions": questions
        }

        return jsonify({"status": "success", "exam": result})

    except Exception as e:
        # 增加日志记录
        print(f"Error in get_exam_questions for exam_id {exam_id}: {e}")
        return jsonify({"status": "error", "message": "获取考试题目时发生内部错误"}), 500

@api_bp.route('/student/exam/submit', methods=['POST'])
@api_login_required
def submit_exam():
    """接收学生提交的考试答案 (API for App)"""
    conn = get_db()
    cursor = conn.cursor()
    try:
        student_id = g.student_id
        data = request.get_json()
        exam_id = data.get('exam_id')
        submitted_answers = data.get('answers') # 格式: [{"question_id": "xxx", "answer": "yyy"}, ...]

        if not exam_id or not isinstance(submitted_answers, list):
            return jsonify({"status": "error", "message": "请求数据不完整或格式错误"}), 400

        # 1. 验证权限、提交状态和截止日期
        cursor.execute("""
            SELECT h.id, h.deadline, h.data
            FROM homework h
            JOIN course_schedules cs ON h.course_schedule_id = cs.id
            JOIN students s ON s.class_id = cs.class_id
            WHERE h.id = ? AND s.student_id = ? AND h.status = 'published'
        """, (exam_id, student_id))
        exam_access = cursor.fetchone()

        if not exam_access:
            return jsonify({"status": "error", "message": "考试不存在、未发布或无权限参与"}), 403

        cursor.execute("SELECT id FROM homework_results WHERE homework_id = ? AND student_id = ?", (exam_id, student_id))
        if cursor.fetchone():
            return jsonify({"status": "error", "message": "您已经提交过此考试，请勿重复提交"}), 409

        deadline_str = exam_access['deadline']
        if deadline_str and datetime.now() > datetime.fromisoformat(deadline_str):
            return jsonify({"status": "error", "message": "已超过考试截止日期"}), 403

        # 2. 获取正确答案并评分
        try:
            homework_data = json.loads(exam_access['data'])
            questions_in_homework = homework_data.get('questions', [])
            if not isinstance(questions_in_homework, list):
                raise ValueError("题目列表格式错误")
        except (json.JSONDecodeError, TypeError):
            return jsonify({"status": "error", "message": "考试题目数据格式错误"}), 500

        # 将学生答案从列表转换为字典以便快速查找
        # 注意：对于手动创建的题目，前端应确保 question_id 是题目的索引(0, 1, 2...)
        student_answers_map = {str(item['question_id']): item['answer'] for item in submitted_answers}

        score = 0
        total_questions = len(questions_in_homework)
        points_per_question = 100 / total_questions if total_questions > 0 else 0

        for index, question_info in enumerate(questions_in_homework):
            question_id_or_index = question_info.get('id', str(index))
            student_answer = student_answers_map.get(str(question_id_or_index))

            # 获取正确答案
            correct_answer = None
            question_type = None

            if 'id' in question_info:
                # 题目来自题库
                cursor.execute("SELECT answer, type FROM exercises WHERE id = ?", (question_info['id'],))
                exercise_record = cursor.fetchone()
                if exercise_record:
                    correct_answer = exercise_record['answer']
                    question_type = exercise_record['type']
            else:
                # 手动创建的题目，答案在自身数据中
                correct_answer = question_info.get('answer')
                question_type = question_info.get('type')

            if correct_answer is not None and student_answer is not None:
                # 对于多选题，答案是逗号分隔的字符串，需要拆分后比较集合
                if question_type == 'multiple':
                    correct_set = set(ans.strip() for ans in str(correct_answer).split(','))
                    student_set = set(ans.strip() for ans in str(student_answer).split(','))
                    if correct_set == student_set:
                        score += points_per_question
                # 其他题目直接比较字符串
                elif str(correct_answer).strip() == str(student_answer).strip():
                    score += points_per_question

        # 3. 存储结果到数据库
        result_id = f"HR_{exam_id}_{student_id}"
        submitted_at = datetime.now().isoformat()
        
        cursor.execute("""
            INSERT INTO homework_results (id, homework_id, student_id, score, data, submitted_at, answers)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (result_id, exam_id, student_id, round(score, 2), json.dumps(submitted_answers), submitted_at, json.dumps(student_answers_map)))

        # 4. 更新作业提交计数
        cursor.execute("UPDATE homework SET submitted_count = submitted_count + 1 WHERE id = ?", (exam_id,))

        conn.commit()

        return jsonify({
            "status": "success",
            "message": "考试提交成功",
            "score": round(score, 2)
        })

    except Exception as e:
        if conn:
            conn.rollback()
        print(f"Error in submit_exam: {e}")
        return jsonify({"status": "error", "message": "提交考试时发生内部错误"}), 500
    finally:
        if conn:
            conn.close()

@api_bp.route('/course/<course_schedule_id>/chat_history', methods=['GET'])
@api_login_required
def get_chat_history(course_schedule_id):
    """获取指定课程的聊天记录（对教师和学生开放）"""
    try:
        conn = get_db()
        cursor = conn.cursor()

        # 验证用户是否有权访问该课程
        user_id = g.user_id
        user_type = g.user_type

        if user_type == 'student':
            cursor.execute("SELECT cs.id FROM course_schedules cs JOIN students s ON cs.class_id = s.class_id WHERE cs.id = ? AND s.student_id = ?", (course_schedule_id, user_id))
        elif user_type == 'teacher':
            cursor.execute("SELECT id FROM course_schedules WHERE id = ? AND teacher_id = ?", (course_schedule_id, user_id))
        else:
            conn.close()
            return jsonify({"status": "error", "message": "未知的用户类型"}), 403
        
        if not cursor.fetchone():
            conn.close()
            return jsonify({"status": "error", "message": "无权访问该课程的聊天记录"}), 403

        # 获取所有用户的弹幕数据
        cursor.execute("""
            SELECT 
                d.user_type,
                d.messages,
                COALESCE(s.name, t.name) as user_name
            FROM danmaku d
            LEFT JOIN students s ON d.user_id = s.student_id AND d.user_type = 'student'
            LEFT JOIN teachers t ON d.user_id = t.teacher_id AND d.user_type = 'teacher'
            WHERE d.course_schedule_id = ?
        """, (course_schedule_id,))
        
        danmaku_records = cursor.fetchall()
        conn.close()

        all_messages = []
        for record in danmaku_records:
            try:
                messages = json.loads(record['messages'])
                for msg in messages:
                    all_messages.append({
                        'user': record['user_name'] or '未知用户',
                        'user_type': record['user_type'],
                        'message': msg.get('text', ''),
                        'timestamp': msg.get('time', '')
                    })
            except (json.JSONDecodeError, TypeError):
                continue
        
        # 按时间戳排序
        all_messages.sort(key=lambda x: x['timestamp'])

        return jsonify({"status": "success", "messages": all_messages})

    except Exception as e:
        print(f"获取聊天记录时出错: {e}")
        return jsonify({"status": "error", "message": "获取聊天记录失败"}), 500