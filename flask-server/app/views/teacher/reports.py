"""
课堂报告相关路由
"""
from flask import render_template, request, jsonify, session, make_response
from app.models.database import get_db
from app.utils.decorators import teacher_required
from datetime import datetime
import json
import csv
import io
import uuid
import json
import csv
import io


def register_routes(bp):
    """注册课堂报告相关路由"""

    @bp.route('/reports', defaults={'course_schedule_id': None})
    @bp.route('/reports/<course_schedule_id>')
    @teacher_required
    def reports(course_schedule_id):
        """课堂报告页面 - 支持实时和历史查看"""
        # 如果没有通过URL传递ID，则尝试从session获取当前进行的课程ID
        if course_schedule_id is None:
            course_schedule_id = session.get('current_class_id')

        if not course_schedule_id:
            # 如果两者都没有，则显示一个提示页面或重定向
            return render_template("teacher/reports.html", current_page="reports", current_class=None, error="请先选择一个课堂")

        conn = get_db()
        cursor = conn.cursor()

        # 获取当前课堂信息
        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                   cl.name as classroom_name, cls.name as class_name,
                   cs.day_of_week, cs.start_time, cs.end_time, cs.status,
                   cs.start_datetime, cs.description
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            WHERE cs.id = ? AND cs.teacher_id = ?
        """, (course_schedule_id, session.get('teacher_id')))

        current_class = cursor.fetchone()
        conn.close()

        if not current_class:
            return render_template("teacher/reports.html", current_page="reports", current_class=None, error="未找到指定的课堂报告或无权限访问")

        return render_template("teacher/reports.html", current_page="reports", current_class=current_class)

    @bp.route('/get_realtime_data/<course_schedule_id>', methods=['GET'])
    @teacher_required
    def get_realtime_data(course_schedule_id):
        """获取课堂实时数据"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证课程权限
            cursor.execute("""
                SELECT cs.id, c.name as course_name, c.code as course_code,
                       cl.name as classroom_name, cls.name as class_name,
                       cs.day_of_week, cs.start_time, cs.end_time
                FROM course_schedules cs
                JOIN courses c ON cs.course_id = c.id
                JOIN classrooms cl ON cs.classroom_id = cl.id
                JOIN classes cls ON cs.class_id = cls.id
                WHERE cs.id = ? AND cs.teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            course_info = cursor.fetchone()
            if not course_info:
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 获取签到统计
            cursor.execute("""
                SELECT COUNT(*) as attendance_count
                FROM class_attendance
                WHERE course_schedule_id = ? AND status = 'present'
            """, (course_schedule_id,))
            attendance_result = cursor.fetchone()
            attendance_count = attendance_result['attendance_count'] if attendance_result else 0

            # 获取班级总人数
            cursor.execute("""
                SELECT COUNT(*) as total_students
                FROM students s
                JOIN course_schedules cs ON cs.class_id = s.class_id
                WHERE cs.id = ?
            """, (course_schedule_id,))
            total_result = cursor.fetchone()
            total_students = total_result['total_students'] if total_result else 0

            # 获取弹幕统计 - 统计总消息数
            cursor.execute("""
                SELECT COALESCE(SUM(message_count), 0) as danmaku_count
                FROM danmaku
                WHERE course_schedule_id = ?
            """, (course_schedule_id,))
            danmaku_result = cursor.fetchone()
            danmaku_count = danmaku_result['danmaku_count'] if danmaku_result else 0

            # 获取发送弹幕的学生数
            cursor.execute("""
                SELECT COUNT(*) as active_students
                FROM danmaku
                WHERE course_schedule_id = ?
            """, (course_schedule_id,))
            active_result = cursor.fetchone()
            active_students = active_result['active_students'] if active_result else 0

            # 获取作业统计
            cursor.execute("""
                SELECT COUNT(*) as homework_count
                FROM homework
                WHERE course_schedule_id = ?
            """, (course_schedule_id,))
            homework_result = cursor.fetchone()
            homework_count = homework_result['homework_count'] if homework_result else 0

            # 获取考勤详情
            cursor.execute("""
                SELECT s.student_id, s.name, a.status, a.signin_time
                FROM students s
                LEFT JOIN class_attendance a ON a.student_id = s.student_id AND a.course_schedule_id = ?
                JOIN course_schedules cs ON cs.class_id = s.class_id
                WHERE cs.id = ?
                ORDER BY s.name
            """, (course_schedule_id, course_schedule_id))
            attendance_details = cursor.fetchall()

            # --- 参与度计算 ---
            # 1. 获取所有学生
            cursor.execute("""
                SELECT s.student_id, s.name
                FROM students s
                JOIN course_schedules cs ON s.class_id = cs.class_id
                WHERE cs.id = ?
            """, (course_schedule_id,))
            all_students_raw = cursor.fetchall()
            student_engagement = {row['student_id']: {'name': row['name'], 'danmaku': 0, 'quiz_participation': 0, 'quiz_correct': 0, 'score': 0} for row in all_students_raw}

            # 2. 聚合弹幕数据
            cursor.execute("SELECT user_id as student_id, message_count FROM danmaku WHERE course_schedule_id = ? AND user_type = 'student'", (course_schedule_id,))
            danmaku_data = cursor.fetchall()
            for row in danmaku_data:
                if row['student_id'] in student_engagement:
                    student_engagement[row['student_id']]['danmaku'] = row['message_count']

            # 3. 聚合答题数据
            cursor.execute("""
                SELECT 
                    qr.student_id, 
                    COUNT(qr.id) as participation_count, 
                    SUM(CASE WHEN qr.is_correct = 1 THEN 1 ELSE 0 END) as correct_count
                FROM quiz_results qr
                JOIN interactive_quiz iq ON qr.quiz_id = iq.id
                WHERE iq.course_schedule_id = ?
                GROUP BY qr.student_id
            """, (course_schedule_id,))
            quiz_results_data = cursor.fetchall()
            for row in quiz_results_data:
                if row['student_id'] in student_engagement:
                    student_engagement[row['student_id']]['quiz_participation'] = row['participation_count']
                    student_engagement[row['student_id']]['quiz_correct'] = row['correct_count']
            
            # 4. 计算总分并生成排行榜
            engagement_ranking = []
            for student_id, data in student_engagement.items():
                score = (data['danmaku'] * 1) + (data['quiz_participation'] * 5) + (data['quiz_correct'] * 10)
                engagement_ranking.append({
                    'student_name': data['name'],
                    'engagement_score': score
                })
            
            # 按分数降序排序
            engagement_ranking.sort(key=lambda x: x['engagement_score'], reverse=True)
            # --- 参与度计算结束 ---

            # 获取作业详情
            cursor.execute("""
                SELECT h.id, h.title, h.created_at, h.data
                FROM homework h
                WHERE h.course_schedule_id = ?
                ORDER BY h.created_at DESC
            """, (course_schedule_id,))
            homework_raw_data = cursor.fetchall()

            # 获取互动答题详情
            cursor.execute("""
                SELECT 
                    iq.id as quiz_id,
                    e.question as question_text,
                    iq.start_time
                FROM interactive_quiz iq
                JOIN exercises e ON iq.exercise_id = e.id
                WHERE iq.course_schedule_id = ?
                ORDER BY iq.start_time DESC
            """, (course_schedule_id,))
            quiz_raw_data = cursor.fetchall()

            # 获取备注
            cursor.execute("SELECT remarks FROM class_reports WHERE course_schedule_id = ?", (course_schedule_id,))
            remarks_result = cursor.fetchone()
            remarks = remarks_result['remarks'] if remarks_result else ''

            # 处理作业数据，计算题目数量和提交情况
            homework_details = []
            for homework in homework_raw_data:
                question_count = 0
                if homework['data']:
                    try:
                        homework_data = json.loads(homework['data'])
                        if isinstance(homework_data, dict):
                            # 新格式: {"questions": [...]}
                            question_count = len(homework_data.get('questions', []))
                        elif isinstance(homework_data, list):
                            # 旧格式: [...]
                            question_count = len(homework_data)
                    except (json.JSONDecodeError, TypeError):
                        # 如果解析失败，题目数记为0
                        question_count = 0
                
                # 获取提交统计
                cursor.execute("""
                    SELECT COUNT(DISTINCT student_id) as submitted_count
                    FROM homework_results
                    WHERE homework_id = ?
                """, (homework['id'],))
                submit_result = cursor.fetchone()
                submitted_count = submit_result['submitted_count'] if submit_result else 0

                homework_details.append({
                    'id': homework['id'],
                    'title': homework['title'],
                    'created_at': homework['created_at'],
                    'question_count': question_count,
                    'submitted_count': submitted_count,
                    'total_students': total_students
                })

            # 处理互动答题数据
            quiz_details = []
            for quiz in quiz_raw_data:
                # 获取参与人数
                cursor.execute("SELECT COUNT(DISTINCT student_id) FROM quiz_results WHERE quiz_id = ?", (quiz['quiz_id'],))
                participants_count = cursor.fetchone()[0]
                
                # 获取正确回答的人数
                cursor.execute("SELECT COUNT(DISTINCT student_id) FROM quiz_results WHERE quiz_id = ? AND is_correct = 1", (quiz['quiz_id'],))
                correct_count = cursor.fetchone()[0]
                
                accuracy = 0
                if participants_count > 0:
                    accuracy = round((correct_count / participants_count) * 100, 1)

                quiz_details.append({
                    'id': quiz['quiz_id'],
                    'question_text': quiz['question_text'],
                    'start_time': quiz['start_time'],
                    'participants_count': participants_count,
                    'correct_count': correct_count,
                    'accuracy': accuracy,
                    'total_students': total_students
                })

            conn.close()

            # 返回实时数据
            response_data = {
                "course_name": course_info['course_name'],
                "classroom_name": course_info['classroom_name'],
                "class_name": course_info['class_name'],
                "report_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "attendance_count": attendance_count,
                "total_students": total_students,
                "danmaku_count": danmaku_count,
                "active_students": active_students,
                "homework_count": homework_count,
                "attendance_details": [dict(row) for row in attendance_details],
                "engagement_ranking": engagement_ranking,
                "homework_details": homework_details,
                "quiz_details": quiz_details,
                "remarks": remarks
            }

            return jsonify({"status": "success", "data": response_data})

        except Exception as e:
            return jsonify({"status": "error", "message": f"获取实时数据失败: {str(e)}"}), 500

    @bp.route('/reports/<course_schedule_id>/remarks', methods=['POST'])
    @teacher_required
    def save_report_remarks(course_schedule_id):
        """保存或更新课堂报告的备注"""
        try:
            data = request.get_json()
            remarks = data.get('remarks', '')
            teacher_id = session.get('teacher_id')

            conn = get_db()
            cursor = conn.cursor()

            # 检查课程是否存在且属于该教师
            cursor.execute("SELECT id FROM course_schedules WHERE id = ? AND teacher_id = ?", (course_schedule_id, teacher_id))
            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "课程不存在或无权限"}), 404

            # 检查是否已有报告记录
            cursor.execute("SELECT id, remarks FROM class_reports WHERE course_schedule_id = ?", (course_schedule_id,))
            report = cursor.fetchone()

            if report:
                # 更新备注
                cursor.execute("""
                    UPDATE class_reports SET remarks = ? WHERE id = ?
                """, (remarks, report['id']))
            else:
                # 插入新报告记录
                report_id = str(uuid.uuid4())
                report_date = datetime.now().date()
                # 获取统计数据以填充报告
                cursor.execute("SELECT COUNT(*) FROM class_attendance WHERE course_schedule_id = ? AND status = 'present'", (course_schedule_id,))
                attendance_count = cursor.fetchone()[0]
                cursor.execute("SELECT COUNT(*) FROM students s JOIN course_schedules cs ON s.class_id = cs.class_id WHERE cs.id = ?", (course_schedule_id,))
                total_students = cursor.fetchone()[0]
                
                cursor.execute("""
                    INSERT INTO class_reports (id, course_schedule_id, teacher_id, report_date, remarks, attendance_count, total_students)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (report_id, course_schedule_id, teacher_id, report_date, remarks, attendance_count, total_students))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "备注保存成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"保存备注失败: {str(e)}"}), 500

    @bp.route('/export_report/<course_schedule_id>', methods=['GET'])
    @teacher_required
    def export_report(course_schedule_id):
        """导出课堂报告"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证课程权限
            cursor.execute("""
                SELECT cs.id, c.name as course_name, c.code as course_code,
                       cl.name as classroom_name, cls.name as class_name,
                       cs.day_of_week, cs.start_time, cs.end_time
                FROM course_schedules cs
                JOIN courses c ON cs.course_id = c.id
                JOIN classrooms cl ON cs.classroom_id = cl.id
                JOIN classes cls ON cs.class_id = cls.id
                WHERE cs.id = ? AND cs.teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            course_info = cursor.fetchone()
            if not course_info:
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 获取签到统计
            cursor.execute("""
                SELECT COUNT(*) as attendance_count
                FROM class_attendance
                WHERE course_schedule_id = ? AND status = 'present'
            """, (course_schedule_id,))
            attendance_result = cursor.fetchone()
            attendance_count = attendance_result['attendance_count'] if attendance_result else 0

            # 获取班级总人数
            cursor.execute("""
                SELECT COUNT(*) as total_students
                FROM students s
                JOIN course_schedules cs ON cs.class_id = s.class_id
                WHERE cs.id = ?
            """, (course_schedule_id,))
            total_result = cursor.fetchone()
            total_students = total_result['total_students'] if total_result else 0

            # 获取弹幕统计 - 统计总消息数
            cursor.execute("""
                SELECT COALESCE(SUM(message_count), 0) as danmaku_count
                FROM danmaku
                WHERE course_schedule_id = ?
            """, (course_schedule_id,))
            danmaku_result = cursor.fetchone()
            danmaku_count = danmaku_result['danmaku_count'] if danmaku_result else 0

            # 获取发送弹幕的学生数
            cursor.execute("""
                SELECT COUNT(*) as active_students
                FROM danmaku
                WHERE course_schedule_id = ?
            """, (course_schedule_id,))
            active_result = cursor.fetchone()
            active_students = active_result['active_students'] if active_result else 0

            # 获取作业统计
            cursor.execute("""
                SELECT COUNT(*) as homework_count
                FROM homework
                WHERE course_schedule_id = ?
            """, (course_schedule_id,))
            homework_result = cursor.fetchone()
            homework_count = homework_result['homework_count'] if homework_result else 0

            # 获取考勤详情
            cursor.execute("""
                SELECT s.student_id, s.name, a.status, a.signin_time
                FROM students s
                LEFT JOIN class_attendance a ON a.student_id = s.student_id AND a.course_schedule_id = ?
                JOIN course_schedules cs ON cs.class_id = s.class_id
                WHERE cs.id = ?
                ORDER BY s.name
            """, (course_schedule_id, course_schedule_id))
            attendance_details = cursor.fetchall()

            # 获取弹幕详情
            cursor.execute("""
                SELECT d.user_id as student_id, s.name as student_name, d.message_count as danmaku_count
                FROM danmaku d
                JOIN students s ON d.user_id = s.student_id
                WHERE d.course_schedule_id = ? AND d.user_type = 'student'
                ORDER BY d.message_count DESC
            """, (course_schedule_id,))
            danmaku_details = cursor.fetchall()

            # 获取作业详情
            cursor.execute("""
                SELECT h.id, h.title, h.created_at, h.data
                FROM homework h
                WHERE h.course_schedule_id = ?
                ORDER BY h.created_at DESC
            """, (course_schedule_id,))
            homework_raw_data = cursor.fetchall()

            conn.close()

            # 生成CSV内容
            output = io.StringIO()
            writer = csv.writer(output)

            # 写入课程信息
            writer.writerow(['课堂报告'])
            writer.writerow(['课程名称', course_info['course_name']])
            writer.writerow(['课程代码', course_info['course_code']])
            writer.writerow(['教室', course_info['classroom_name']])
            writer.writerow(['班级', course_info['class_name']])
            writer.writerow(['时间', f"{course_info['day_of_week']} {course_info['start_time']}-{course_info['end_time']}"])
            writer.writerow(['报告生成时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
            writer.writerow([])

            # 写入统计概览
            writer.writerow(['统计概览'])
            writer.writerow(['签到人数', attendance_count])
            writer.writerow(['班级总人数', total_students])
            attendance_rate = round((attendance_count / total_students) * 100, 1) if total_students > 0 else 0
            writer.writerow(['出勤率', f"{attendance_rate}%"])
            writer.writerow(['弹幕总数', danmaku_count])
            writer.writerow(['互动学生数', active_students])
            interaction_rate = round((active_students / total_students) * 100, 1) if total_students > 0 else 0
            writer.writerow(['互动率', f"{interaction_rate}%"])
            writer.writerow(['作业数量', homework_count])
            writer.writerow([])

            # 写入考勤详情
            writer.writerow(['考勤详情'])
            writer.writerow(['学号', '姓名', '签到状态', '签到时间'])
            for student in attendance_details:
                status_text = '已签到' if student['status'] == 'present' else '未签到'
                signin_time = student['signin_time'][:19] if student['signin_time'] else ''
                writer.writerow([student['student_id'], student['name'], status_text, signin_time])
            writer.writerow([])

            # 写入弹幕互动详情
            writer.writerow(['弹幕互动详情'])
            writer.writerow(['学号', '姓名', '发送弹幕数'])
            for student in danmaku_details:
                writer.writerow([student['student_id'], student['student_name'], student['danmaku_count']])
            writer.writerow([])

            # 写入作业详情
            if homework_raw_data:
                writer.writerow(['作业详情'])
                writer.writerow(['作业标题', '创建时间', '题目数量'])
                for homework in homework_raw_data:
                    homework_data = json.loads(homework['data']) if homework['data'] else {}
                    question_count = len(homework_data.get('questions', []))
                    writer.writerow([homework['title'], homework['created_at'][:19], question_count])

            # 创建响应
            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'text/csv; charset=utf-8-sig'
            filename = f"class_report_{course_info['course_code']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            response.headers['Content-Disposition'] = f'attachment; filename={filename}'

            return response

        except Exception as e:
            return jsonify({"status": "error", "message": f"导出失败: {str(e)}"}), 500
