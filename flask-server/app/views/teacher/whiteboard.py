# flask-server/app/views/teacher/whiteboard.py
from flask import render_template, jsonify, request, session
from app.utils.decorators import teacher_required
from app.models.database import get_db
from app.services.whiteboard_service import whiteboard_service
from datetime import datetime
import json


def register_routes(bp):
    @bp.route('/whiteboard_management')
    @teacher_required
    def whiteboard_management():
        """白板管理页面"""
        return render_template('teacher/whiteboard_management.html')

    @bp.route('/api/whiteboard/sessions', methods=['GET'])
    @teacher_required
    def get_whiteboard_sessions():
        """获取所有白板会话"""
        try:
            teacher_id = session.get('teacher_id')
            conn = get_db()
            cursor = conn.cursor()
            
            # 获取当前教师的所有活跃白板会话
            cursor.execute("""
                SELECT ws.id, ws.group_id, ws.session_name, ws.collaboration_enabled,
                       ws.created_at, ws.updated_at,
                       cg.group_name, cg.group_description,
                       cs.id as course_schedule_id,
                       c.name as course_name
                FROM whiteboard_sessions ws
                JOIN class_groups cg ON ws.group_id = cg.id
                JOIN course_schedules cs ON ws.course_schedule_id = cs.id
                JOIN courses c ON cs.course_id = c.id
                WHERE ws.created_by = ? AND ws.status = 'active'
                ORDER BY ws.updated_at DESC
            """, (teacher_id,))
            
            sessions = []
            for row in cursor.fetchall():
                # 获取参与者数量
                cursor.execute("""
                    SELECT COUNT(*) as count FROM whiteboard_participants 
                    WHERE session_id = ? AND is_online = 1
                """, (row['id'],))
                participant_count = cursor.fetchone()['count']
                
                sessions.append({
                    'session_id': row['id'],
                    'group_id': row['group_id'],
                    'group_name': row['group_name'],
                    'session_name': row['session_name'],
                    'collaboration_enabled': bool(row['collaboration_enabled']),
                    'participant_count': participant_count,
                    'course_name': row['course_name'],
                    'created_at': row['created_at'],
                    'updated_at': row['updated_at']
                })
            
            conn.close()
            return jsonify(success=True, sessions=sessions)
            
        except Exception as e:
            return jsonify(success=False, message=f"获取白板会话失败: {str(e)}"), 500

    @bp.route('/api/whiteboard/sessions/<session_id>/details', methods=['GET'])
    @teacher_required
    def get_session_details(session_id):
        """获取白板会话详情"""
        try:
            # 获取会话内容
            content = whiteboard_service.get_session_content(session_id)
            
            # 获取参与者列表
            participants = whiteboard_service.get_session_participants(session_id)
            
            # 获取会话基本信息
            conn = get_db()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT ws.*, cg.group_name, c.name as course_name
                FROM whiteboard_sessions ws
                JOIN class_groups cg ON ws.group_id = cg.id
                JOIN course_schedules cs ON ws.course_schedule_id = cs.id
                JOIN courses c ON cs.course_id = c.id
                WHERE ws.id = ?
            """, (session_id,))
            
            session_info = cursor.fetchone()
            conn.close()
            
            if not session_info:
                return jsonify(success=False, message="会话不存在"), 404
            
            return jsonify(
                success=True,
                session_info={
                    'session_id': session_info['id'],
                    'group_name': session_info['group_name'],
                    'session_name': session_info['session_name'],
                    'course_name': session_info['course_name'],
                    'collaboration_enabled': bool(session_info['collaboration_enabled']),
                    'created_at': session_info['created_at'],
                    'updated_at': session_info['updated_at']
                },
                content=content,
                participants=participants
            )
            
        except Exception as e:
            return jsonify(success=False, message=f"获取会话详情失败: {str(e)}"), 500

    @bp.route('/api/whiteboard/sessions/<session_id>/export', methods=['GET'])
    @teacher_required
    def export_whiteboard_session(session_id):
        """导出白板会话内容"""
        try:
            # 获取会话内容
            content = whiteboard_service.get_session_content(session_id)
            
            if not content:
                return jsonify(success=False, message="会话内容不存在"), 404
            
            # 获取会话信息
            conn = get_db()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT ws.session_name, cg.group_name, c.name as course_name
                FROM whiteboard_sessions ws
                JOIN class_groups cg ON ws.group_id = cg.id
                JOIN course_schedules cs ON ws.course_schedule_id = cs.id
                JOIN courses c ON cs.course_id = c.id
                WHERE ws.id = ?
            """, (session_id,))
            
            session_info = cursor.fetchone()
            conn.close()
            
            # 构建导出数据
            export_data = {
                'session_info': {
                    'session_id': session_id,
                    'session_name': session_info['session_name'] if session_info else '未知会话',
                    'group_name': session_info['group_name'] if session_info else '未知分组',
                    'course_name': session_info['course_name'] if session_info else '未知课程',
                    'export_time': datetime.now().isoformat()
                },
                'content': content
            }
            
            return jsonify(success=True, data=export_data)
            
        except Exception as e:
            return jsonify(success=False, message=f"导出失败: {str(e)}"), 500

    @bp.route('/api/whiteboard/sessions/<session_id>/clear', methods=['POST'])
    @teacher_required
    def clear_whiteboard_session(session_id):
        """清空白板会话内容"""
        try:
            teacher_id = session.get('teacher_id')
            
            # 创建空白内容
            empty_content = {
                "elements": [],
                "appState": {
                    "viewBackgroundColor": "#ffffff",
                    "currentItemStrokeColor": "#000000",
                    "currentItemBackgroundColor": "transparent"
                }
            }
            
            # 保存空白内容
            success = whiteboard_service.save_content(
                session_id, 
                empty_content["elements"], 
                empty_content["appState"], 
                teacher_id, 
                is_snapshot=True
            )
            
            if success:
                # 通知所有参与者更新
                from app import socketio
                socketio.emit('whiteboard_update', {
                    'session_id': session_id,
                    'elements': empty_content["elements"],
                    'appState': empty_content["appState"],
                    'user_id': teacher_id,
                    'timestamp': int(datetime.now().timestamp() * 1000)
                }, room=f"whiteboard_{session_id}")
                
                return jsonify(success=True, message="白板已清空")
            else:
                return jsonify(success=False, message="清空失败"), 500
                
        except Exception as e:
            return jsonify(success=False, message=f"清空白板失败: {str(e)}"), 500

    @bp.route('/api/whiteboard/sessions/<session_id>/snapshot', methods=['POST'])
    @teacher_required
    def create_whiteboard_snapshot(session_id):
        """创建白板快照"""
        try:
            teacher_id = session.get('teacher_id')
            
            # 获取当前内容
            content = whiteboard_service.get_session_content(session_id)
            
            if not content:
                return jsonify(success=False, message="无法获取当前内容"), 404
            
            # 创建快照
            success = whiteboard_service.save_content(
                session_id,
                content.get('elements', []),
                content.get('appState', {}),
                teacher_id,
                is_snapshot=True
            )
            
            if success:
                return jsonify(success=True, message="快照创建成功")
            else:
                return jsonify(success=False, message="快照创建失败"), 500
                
        except Exception as e:
            return jsonify(success=False, message=f"创建快照失败: {str(e)}"), 500

    @bp.route('/api/whiteboard/sessions/<session_id>/history', methods=['GET'])
    @teacher_required
    def get_whiteboard_history(session_id):
        """获取白板历史版本"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, version, created_by, created_at, is_snapshot
                FROM whiteboard_contents
                WHERE session_id = ?
                ORDER BY version DESC
                LIMIT 20
            """, (session_id,))
            
            history = []
            for row in cursor.fetchall():
                history.append({
                    'content_id': row['id'],
                    'version': row['version'],
                    'created_by': row['created_by'],
                    'created_at': row['created_at'],
                    'is_snapshot': bool(row['is_snapshot'])
                })
            
            conn.close()
            return jsonify(success=True, history=history)
            
        except Exception as e:
            return jsonify(success=False, message=f"获取历史失败: {str(e)}"), 500

    @bp.route('/api/whiteboard/sessions/<session_id>/operations', methods=['GET'])
    @teacher_required
    def get_whiteboard_operations(session_id):
        """获取白板操作记录"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            # 获取最近的操作记录
            cursor.execute("""
                SELECT user_id, user_type, operation_type, timestamp, created_at
                FROM whiteboard_operations
                WHERE session_id = ?
                ORDER BY sequence_number DESC
                LIMIT 50
            """, (session_id,))
            
            operations = []
            for row in cursor.fetchall():
                operations.append({
                    'user_id': row['user_id'],
                    'user_type': row['user_type'],
                    'operation_type': row['operation_type'],
                    'timestamp': row['timestamp'],
                    'created_at': row['created_at']
                })
            
            conn.close()
            return jsonify(success=True, operations=operations)
            
        except Exception as e:
            return jsonify(success=False, message=f"获取操作记录失败: {str(e)}"), 500
