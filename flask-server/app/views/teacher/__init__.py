from flask import Blueprint
from . import auth, course_management, attendance, homework, reports, whiteboard

teacher_bp = Blueprint('teacher', __name__, url_prefix='/teacher')

# 注册子模块的路由
auth.register_routes(teacher_bp)
course_management.register_routes(teacher_bp)
attendance.register_routes(teacher_bp)
homework.register_routes(teacher_bp)
reports.register_routes(teacher_bp)
whiteboard.register_routes(teacher_bp)
