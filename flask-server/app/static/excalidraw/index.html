<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <title>Excalidraw</title>
    <link rel="stylesheet" href="./assets/index-Bnrc8hOs.css">
    <style>
        /* --- 自定义Excalidraw UI--- */

        /* 按钮 */
        .excalidraw button[data-testid="collab-button"] {
            display: none !important;
        }
        .excalidraw .collab-button {
            display: none !important;
        }
        .excalidraw .help-icon {
            display: none !important;
        }

        /* 外部链接 */
        .excalidraw a[href^="https://discord.gg/UexuTaE"]{
            display: none !important;
        }
        .excalidraw a[href^="https://x.com/excalidraw"]{
            display: none !important;
        }
        .excalidraw a[href^="https://github.com/excalidraw/excalidraw"]{
            display: none !important;
        }
        .excalidraw a[href^="https://plus.excalidraw.com/plus?utm_source=excalidraw&utm_medium=app&utm_content=hamburger"]{
            display: none !important;
        }
        .excalidraw a[href^="https://app.excalidraw.com/sign-up"]{
            display: none !important;
        }
        .excalidraw a[href^="https://youtube.com/@excalidraw"]{
            display: none !important;
        }
        .excalidraw a[href^="https://plus.excalidraw.com/blog"]{
            display: none !important;
        }


        html, body, #root {
            height: 100%;
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 18px;
            color: #666;
        }
        .error {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 18px;
            color: #d32f2f;
            flex-direction: column;
        }
        .error button {
            margin-top: 20px;
            padding: 10px 20px;
            background: #1976d2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">正在加载 Excalidraw...</div>
    </div>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>

    <!-- 引入 Excalidraw -->
    <script type="module">
        let socket;
        let groupInfo = null;
        let excalidrawAPI = null;

        // 简化的错误处理
        function showError(message) {
            const root = document.getElementById('root');
            root.innerHTML = `
                <div class="error">
                    <div>错误: ${message}</div>
                    <button onclick="location.reload()">重新加载</button>
                </div>
            `;
        }

        // 检查必要的依赖
        if (typeof React === 'undefined') {
            showError('React 库加载失败');
        } else if (typeof ReactDOM === 'undefined') {
            showError('ReactDOM 库加载失败');
        } else if (typeof io === 'undefined') {
            showError('Socket.IO 库加载失败');
        } else {
            // 尝试直接使用全局的Excalidraw
            initializeExcalidraw();
        }

        async function initializeExcalidraw() {
            try {
                // 等待一下确保所有依赖都加载完成
                await new Promise(resolve => setTimeout(resolve, 100));

                // 初始化 Socket.IO 连接
                socket = io();

                socket.on('connect', () => {
                    console.log('已连接到服务器');
                    // 通过 postMessage 通知父窗口
                    window.parent.postMessage({
                        type: 'EXCALIDRAW_READY',
                        payload: { connected: true }
                    }, '*');
                });

                socket.on('whiteboard_update', (data) => {
                    if (excalidrawAPI && data.elements) {
                        try {
                            excalidrawAPI.updateScene({
                                elements: data.elements,
                                appState: data.appState || {},
                                commitToHistory: false
                            });
                        } catch (error) {
                            console.error('更新场景失败:', error);
                        }
                    }
                });

                // 监听来自父窗口的消息
                window.addEventListener('message', (event) => {
                    if (event.data.type === 'JOIN_GROUP') {
                        groupInfo = event.data.payload;
                        if (socket && groupInfo) {
                            socket.emit('join_whiteboard', { room: groupInfo.group_id });
                        }
                    }
                });

                // 创建一个简单的绘图界面
                const App = () => {
                    const [isReady, setIsReady] = React.useState(false);
                    const [error, setError] = React.useState(null);

                    React.useEffect(() => {
                        // 尝试动态加载Excalidraw
                        const loadExcalidraw = async () => {
                            try {
                                // 尝试从全局变量获取
                                if (window.ExcalidrawLib) {
                                    setIsReady(true);
                                    return;
                                }

                                // 尝试动态导入
                                const module = await import('./assets/index-dxpBGHC9.js');
                                window.ExcalidrawLib = module;
                                setIsReady(true);
                            } catch (err) {
                                console.error('加载Excalidraw失败:', err);
                                setError('无法加载绘图组件');
                            }
                        };

                        loadExcalidraw();
                    }, []);

                    const handleChange = React.useCallback((elements, appState, files) => {
                        // 避免在拖拽等操作时频繁发送更新
                        if (appState && (appState.draggingElement ||
                            appState.resizingElement ||
                            appState.editingElement ||
                            appState.writing)) {
                            return;
                        }

                        if (socket && groupInfo) {
                            socket.emit('whiteboard_update', {
                                elements: elements,
                                appState: appState ? {
                                    viewBackgroundColor: appState.viewBackgroundColor,
                                    scrollX: appState.scrollX,
                                    scrollY: appState.scrollY,
                                    zoom: appState.zoom
                                } : {}
                            });
                        }

                        // 通知父窗口内容已更改
                        window.parent.postMessage({
                            type: 'EXCALIDRAW_CHANGE',
                            payload: { elements, appState }
                        }, '*');
                    }, []);

                    if (error) {
                        return React.createElement('div', { className: 'error' },
                            React.createElement('div', null, error),
                            React.createElement('button', {
                                onClick: () => location.reload()
                            }, '重新加载')
                        );
                    }

                    if (!isReady) {
                        return React.createElement('div', { className: 'loading' }, '正在加载绘图组件...');
                    }

                    // 尝试创建Excalidraw组件
                    try {
                        const ExcalidrawComponent = window.ExcalidrawLib?.Excalidraw ||
                                                  window.ExcalidrawLib?.default?.Excalidraw ||
                                                  window.ExcalidrawLib?.default;

                        if (!ExcalidrawComponent) {
                            return React.createElement('div', { className: 'error' },
                                '找不到Excalidraw组件'
                            );
                        }

                        return React.createElement(ExcalidrawComponent, {
                            ref: (api) => {
                                excalidrawAPI = api;
                            },
                            onChange: handleChange,
                            langCode: "zh-CN",
                            theme: "light",
                            initialData: {
                                elements: [],
                                appState: {
                                    viewBackgroundColor: "#ffffff"
                                }
                            }
                        });
                    } catch (err) {
                        console.error('创建Excalidraw组件失败:', err);
                        return React.createElement('div', { className: 'error' },
                            '创建绘图组件失败'
                        );
                    }
                };

                // 渲染应用
                const root = ReactDOM.createRoot(document.getElementById('root'));
                root.render(React.createElement(App));

            } catch (error) {
                showError('Excalidraw 初始化失败: ' + error.message);
            }
        }
    </script>
</body>
</html>