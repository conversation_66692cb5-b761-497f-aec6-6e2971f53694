<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <title>Excalidraw</title>
    <link rel="stylesheet" href="./assets/index-Bnrc8hOs.css">
    <style>
        /* --- 自定义Excalidraw UI--- */

        /* 按钮 */
        .excalidraw button[data-testid="collab-button"] {
            display: none !important;
        }
        .excalidraw .collab-button {
            display: none !important;
        }
        .excalidraw .help-icon {
            display: none !important;
        }

        /* 欢迎菜单*/
        .excalidraw .welcome-screen-menu {
            display: none !important;
        }
        

        /* 外部链接 */
        .excalidraw a[href^="https://discord.gg/UexuTaE"]{
            display: none !important;
        }
        .excalidraw a[href^="https://x.com/excalidraw"]{
            display: none !important;
        }
        .excalidraw a[href^="https://github.com/excalidraw/excalidraw"]{
            display: none !important;
        }
        .excalidraw a[href^="https://plus.excalidraw.com/plus?utm_source=excalidraw&utm_medium=app&utm_content=hamburger"]{
            display: none !important;
        }
        .excalidraw a[href^="https://app.excalidraw.com/sign-up"]{
            display: none !important;
        }
        .excalidraw a[href^="https://youtube.com/@excalidraw"]{
            display: none !important;
        }
        .excalidraw a[href^="https://plus.excalidraw.com/blog"]{
            display: none !important;
        }


        html, body, #root {
            height: 100%;
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        }
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 18px;
            color: #666;
        }
        .error {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            font-size: 18px;
            color: #d32f2f;
            flex-direction: column;
        }
        .error button {
            margin-top: 20px;
            padding: 10px 20px;
            background: #1976d2;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div id="root">
        <div class="loading">正在加载 Excalidraw...</div>
    </div>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>

    <!-- 引入 Excalidraw -->
    <script type="module">
        let socket;
        let groupInfo = null;
        let excalidrawAPI = null;
        let sessionId = null;
        let currentUserId = null;
        let currentUserName = null;
        let collaborationEnabled = true;
        let participants = new Map();
        let isUpdatingFromRemote = false;
        let lastUpdateTime = 0;
        let autoSaveInterval = null;
        let reconnectAttempts = 0;
        let maxReconnectAttempts = 5;

        // 简化的错误处理
        function showError(message) {
            const root = document.getElementById('root');
            root.innerHTML = `
                <div class="error">
                    <div>错误: ${message}</div>
                    <button onclick="location.reload()">重新加载</button>
                </div>
            `;
        }

        // 检查必要的依赖
        if (typeof React === 'undefined') {
            showError('React 库加载失败');
        } else if (typeof ReactDOM === 'undefined') {
            showError('ReactDOM 库加载失败');
        } else if (typeof io === 'undefined') {
            showError('Socket.IO 库加载失败');
        } else {
            // 尝试直接使用全局的Excalidraw
            initializeExcalidraw();
        }

        async function initializeExcalidraw() {
            try {
                // 等待一下确保所有依赖都加载完成
                await new Promise(resolve => setTimeout(resolve, 100));

                // 初始化 Socket.IO 连接
                socket = io();

                socket.on('connect', () => {
                    console.log('Socket.IO已连接到服务器');
                    reconnectAttempts = 0; // 重置重连计数

                    // 如果是重连，尝试重新加入会话
                    if (sessionId && currentUserId) {
                        console.log('检测到重连，尝试重新加入会话...');
                        rejoinSession();
                    }

                    // 通过 postMessage 通知父窗口
                    window.parent.postMessage({
                        type: 'EXCALIDRAW_READY',
                        payload: { connected: true }
                    }, '*');
                });

                socket.on('disconnect', () => {
                    console.log('与服务器断开连接');
                    showNotification('连接已断开，正在尝试重连...', 'warning');

                    // 停止自动保存
                    if (autoSaveInterval) {
                        clearInterval(autoSaveInterval);
                        autoSaveInterval = null;
                    }
                });

                socket.on('reconnect', () => {
                    console.log('已重新连接到服务器');
                    showNotification('连接已恢复', 'success');

                    // 重新启动自动保存
                    startAutoSave();
                });

                // 处理白板更新
                socket.on('whiteboard_update', (data) => {
                    if (excalidrawAPI && data.elements && !isUpdatingFromRemote) {
                        try {
                            isUpdatingFromRemote = true;
                            excalidrawAPI.updateScene({
                                elements: data.elements,
                                appState: data.appState || {},
                                commitToHistory: false
                            });
                            lastUpdateTime = Date.now();
                        } catch (error) {
                            console.error('更新场景失败:', error);
                        } finally {
                            setTimeout(() => {
                                isUpdatingFromRemote = false;
                            }, 100);
                        }
                    }
                });

                // 处理用户加入
                socket.on('whiteboard_user_joined', (data) => {
                    participants.set(data.user_id, {
                        name: data.user_name,
                        type: data.user_type,
                        online: true
                    });
                    updateParticipantsList();
                    showNotification(`${data.user_name} 加入了白板`);
                });

                // 处理用户离开
                socket.on('whiteboard_user_left', (data) => {
                    participants.delete(data.user_id);
                    updateParticipantsList();
                    showNotification(`用户离开了白板`);
                });

                // 处理协作状态变化
                socket.on('whiteboard_collaboration_status', (data) => {
                    collaborationEnabled = data.enabled;
                    updateCollaborationStatus();
                    showNotification(`协作模式已${data.enabled ? '开启' : '关闭'}`);
                });

                // 处理白板加入响应
                socket.on('whiteboard_join_response', (data) => {
                    if (data.success) {
                        sessionId = data.session_id;

                        // 更新参与者列表
                        participants.clear();
                        if (data.participants) {
                            data.participants.forEach(p => {
                                participants.set(p.user_id, {
                                    name: p.user_name,
                                    type: p.user_type,
                                    online: p.is_online
                                });
                            });
                        }
                        updateParticipantsList();

                        // 加载初始内容
                        if (data.content && excalidrawAPI) {
                            isUpdatingFromRemote = true;
                            excalidrawAPI.updateScene({
                                elements: data.content.elements || [],
                                appState: data.content.appState || {},
                                commitToHistory: false
                            });
                            setTimeout(() => {
                                isUpdatingFromRemote = false;
                            }, 100);
                        }

                        // 启动自动保存
                        startAutoSave();

                        showNotification('成功加入协同白板');
                    } else {
                        showNotification(`加入失败: ${data.error}`, 'error');
                    }
                });

                // 处理错误
                socket.on('whiteboard_error', (data) => {
                    showNotification(`错误: ${data.message}`, 'error');
                });

                // 监听来自父窗口的消息
                window.addEventListener('message', (event) => {
                    if (event.data.type === 'JOIN_GROUP') {
                        groupInfo = event.data.payload;
                        currentUserId = groupInfo.student_id || 'anonymous';
                        currentUserName = groupInfo.student_name || '匿名用户';

                        console.log('收到JOIN_GROUP消息:', {
                            student_id: currentUserId,
                            student_name: currentUserName,
                            group_id: groupInfo.group_id,
                            course_schedule_id: groupInfo.course_schedule_id
                        });

                        if (socket && groupInfo) {
                            console.log('发送collaborative_whiteboard_join事件...');
                            // 使用新的协同白板加入事件
                            socket.emit('collaborative_whiteboard_join', {
                                student_id: currentUserId,
                                student_name: currentUserName,
                                group_id: groupInfo.group_id,
                                course_schedule_id: groupInfo.course_schedule_id
                            });
                        } else {
                            console.error('Socket未连接或分组信息缺失:', { socket: !!socket, groupInfo });
                        }
                    }
                });

                // 创建一个简单的绘图界面
                const App = () => {
                    const [isReady, setIsReady] = React.useState(false);
                    const [error, setError] = React.useState(null);

                    React.useEffect(() => {
                        // 尝试动态加载Excalidraw
                        const loadExcalidraw = async () => {
                            try {
                                // 尝试从全局变量获取
                                if (window.ExcalidrawLib) {
                                    setIsReady(true);
                                    return;
                                }

                                // 尝试动态导入
                                const module = await import('./assets/index-dxpBGHC9.js');
                                window.ExcalidrawLib = module;
                                setIsReady(true);
                            } catch (err) {
                                console.error('加载Excalidraw失败:', err);
                                setError('无法加载绘图组件');
                            }
                        };

                        loadExcalidraw();
                    }, []);

                    const handleChange = React.useCallback((elements, appState, files) => {
                        // 如果是远程更新触发的，不要再次发送
                        if (isUpdatingFromRemote) {
                            return;
                        }

                        // 避免在拖拽等操作时频繁发送更新
                        if (appState && (appState.draggingElement ||
                            appState.resizingElement ||
                            appState.editingElement ||
                            appState.writing)) {
                            return;
                        }

                        // 防抖处理，避免过于频繁的更新
                        const now = Date.now();
                        if (now - lastUpdateTime < 100) {
                            return;
                        }
                        lastUpdateTime = now;

                        if (socket && sessionId && collaborationEnabled) {
                            socket.emit('whiteboard_update', {
                                session_id: sessionId,
                                user_id: currentUserId,
                                elements: elements,
                                appState: appState ? {
                                    viewBackgroundColor: appState.viewBackgroundColor,
                                    scrollX: appState.scrollX,
                                    scrollY: appState.scrollY,
                                    zoom: appState.zoom
                                } : {}
                            });
                        }

                        // 通知父窗口内容已更改
                        window.parent.postMessage({
                            type: 'EXCALIDRAW_CHANGE',
                            payload: { elements, appState }
                        }, '*');
                    }, []);

                    if (error) {
                        return React.createElement('div', { className: 'error' },
                            React.createElement('div', null, error),
                            React.createElement('button', {
                                onClick: () => location.reload()
                            }, '重新加载')
                        );
                    }

                    if (!isReady) {
                        return React.createElement('div', { className: 'loading' }, '正在加载绘图组件...');
                    }

                    // 尝试创建Excalidraw组件
                    try {
                        const ExcalidrawComponent = window.ExcalidrawLib?.Excalidraw ||
                                                  window.ExcalidrawLib?.default?.Excalidraw ||
                                                  window.ExcalidrawLib?.default;

                        if (!ExcalidrawComponent) {
                            return React.createElement('div', { className: 'error' },
                                '找不到Excalidraw组件'
                            );
                        }

                        return React.createElement(ExcalidrawComponent, {
                            ref: (api) => {
                                excalidrawAPI = api;
                            },
                            onChange: handleChange,
                            langCode: "zh-CN",
                            theme: "light",
                            initialData: {
                                elements: [],
                                appState: {
                                    viewBackgroundColor: "#ffffff"
                                }
                            }
                        });
                    } catch (err) {
                        console.error('创建Excalidraw组件失败:', err);
                        return React.createElement('div', { className: 'error' },
                            '创建绘图组件失败'
                        );
                    }
                };

                // 渲染应用
                const root = ReactDOM.createRoot(document.getElementById('root'));
                root.render(React.createElement(App));

            } catch (error) {
                showError('Excalidraw 初始化失败: ' + error.message);
            }
        }

        // 辅助函数
        function updateParticipantsList() {
            // 通知父窗口更新参与者列表
            const participantsList = Array.from(participants.entries()).map(([id, info]) => ({
                id,
                name: info.name,
                type: info.type,
                online: info.online
            }));

            window.parent.postMessage({
                type: 'PARTICIPANTS_UPDATE',
                payload: { participants: participantsList }
            }, '*');
        }

        function updateCollaborationStatus() {
            window.parent.postMessage({
                type: 'COLLABORATION_STATUS',
                payload: { enabled: collaborationEnabled }
            }, '*');
        }

        function showNotification(message, type = 'info') {
            console.log(`[${type.toUpperCase()}] ${message}`);

            // 通知父窗口显示消息
            window.parent.postMessage({
                type: 'SHOW_NOTIFICATION',
                payload: { message, type }
            }, '*');
        }

        // 自动保存功能
        function startAutoSave() {
            if (autoSaveInterval) {
                clearInterval(autoSaveInterval);
            }

            // 每30秒自动保存一次
            autoSaveInterval = setInterval(() => {
                if (excalidrawAPI && sessionId && currentUserId) {
                    const elements = excalidrawAPI.getSceneElements();
                    const appState = excalidrawAPI.getAppState();

                    if (elements && elements.length > 0) {
                        saveContent(elements, appState, true); // 标记为自动保存
                    }
                }
            }, 30000);
        }

        function saveContent(elements, appState, isAutoSave = false) {
            if (socket && sessionId && currentUserId) {
                socket.emit('whiteboard_update', {
                    session_id: sessionId,
                    user_id: currentUserId,
                    elements: elements,
                    appState: appState ? {
                        viewBackgroundColor: appState.viewBackgroundColor,
                        scrollX: appState.scrollX,
                        scrollY: appState.scrollY,
                        zoom: appState.zoom
                    } : {},
                    is_auto_save: isAutoSave
                });

                if (!isAutoSave) {
                    console.log('手动保存白板内容');
                }
            }
        }

        function rejoinSession() {
            if (socket && groupInfo && currentUserId && currentUserName) {
                console.log('尝试重新加入白板会话...');
                socket.emit('collaborative_whiteboard_join', {
                    student_id: currentUserId,
                    student_name: currentUserName,
                    group_id: groupInfo.group_id,
                    course_schedule_id: groupInfo.course_schedule_id
                });
            }
        }

        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            if (autoSaveInterval) {
                clearInterval(autoSaveInterval);
            }

            if (socket && sessionId && currentUserId) {
                socket.emit('whiteboard_leave', {
                    session_id: sessionId,
                    user_id: currentUserId
                });
            }
        });

        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // 页面隐藏时停止自动保存
                if (autoSaveInterval) {
                    clearInterval(autoSaveInterval);
                    autoSaveInterval = null;
                }
            } else {
                // 页面显示时重新启动自动保存
                if (sessionId && !autoSaveInterval) {
                    startAutoSave();
                }
            }
        });
    </script>
</body>
</html>