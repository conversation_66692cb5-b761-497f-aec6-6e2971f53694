# flask-server/app/tasks/whiteboard_cleanup.py
"""
白板清理任务
定期清理不活跃的白板会话、过期的操作记录等
"""

import threading
import time
from datetime import datetime, timedelta
from app.services.whiteboard_service import whiteboard_service
from app.models.database import get_db


class WhiteboardCleanupTask:
    """白板清理任务类"""

    def __init__(self, interval_hours: int = 1):
        self.interval_hours = interval_hours
        self.running = False
        self.thread = None
        self.app = None
    
    def start(self, app=None):
        """启动清理任务"""
        if self.running:
            return

        self.app = app
        self.running = True
        self.thread = threading.Thread(target=self._run_cleanup_loop, daemon=True)
        self.thread.start()
        print(f"白板清理任务已启动，清理间隔: {self.interval_hours} 小时")
    
    def stop(self):
        """停止清理任务"""
        self.running = False
        if self.thread:
            self.thread.join()
        print("白板清理任务已停止")
    
    def _run_cleanup_loop(self):
        """运行清理循环"""
        while self.running:
            try:
                if self.app:
                    with self.app.app_context():
                        self.run_cleanup()
                else:
                    self.run_cleanup()
                # 等待下一次清理
                time.sleep(self.interval_hours * 3600)
            except Exception as e:
                print(f"白板清理任务异常: {e}")
                time.sleep(300)  # 出错时等待5分钟后重试
    
    def run_cleanup(self):
        """执行清理操作"""
        print(f"开始执行白板清理任务 - {datetime.now()}")
        
        try:
            # 1. 清理不活跃的会话
            inactive_sessions = whiteboard_service.cleanup_inactive_sessions(24)
            print(f"清理了 {inactive_sessions} 个不活跃的白板会话")
            
            # 2. 清理离线用户
            offline_users = self._cleanup_offline_users()
            print(f"清理了 {offline_users} 个离线用户记录")
            
            # 3. 清理过期的操作记录
            old_operations = self._cleanup_old_operations()
            print(f"清理了 {old_operations} 条过期操作记录")
            
            # 4. 清理过期的内容版本
            old_contents = self._cleanup_old_content_versions()
            print(f"清理了 {old_contents} 个过期内容版本")
            
            # 5. 优化数据库
            self._optimize_database()
            
            print(f"白板清理任务完成 - {datetime.now()}")
            
        except Exception as e:
            print(f"白板清理任务执行失败: {e}")
    
    def _cleanup_offline_users(self) -> int:
        """清理长时间离线的用户记录"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            # 清理超过24小时离线的用户记录
            cutoff_time = datetime.now() - timedelta(hours=24)
            cursor.execute("""
                DELETE FROM whiteboard_participants 
                WHERE is_online = 0 AND left_at < ?
            """, (cutoff_time.isoformat(),))
            
            cleaned_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            return cleaned_count
            
        except Exception as e:
            print(f"清理离线用户失败: {e}")
            return 0
    
    def _cleanup_old_operations(self) -> int:
        """清理过期的操作记录"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            # 清理超过7天的操作记录
            cutoff_time = datetime.now() - timedelta(days=7)
            cursor.execute("""
                DELETE FROM whiteboard_operations 
                WHERE created_at < ?
            """, (cutoff_time.isoformat(),))
            
            cleaned_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            return cleaned_count
            
        except Exception as e:
            print(f"清理操作记录失败: {e}")
            return 0
    
    def _cleanup_old_content_versions(self) -> int:
        """清理过期的内容版本"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            # 为每个会话保留最近30个版本和所有快照
            cursor.execute("""
                SELECT session_id, COUNT(*) as version_count 
                FROM whiteboard_contents 
                WHERE is_snapshot = 0
                GROUP BY session_id 
                HAVING version_count > 30
            """)
            
            sessions_to_clean = cursor.fetchall()
            total_cleaned = 0
            
            for session in sessions_to_clean:
                session_id = session['session_id']
                
                # 获取要保留的版本ID
                cursor.execute("""
                    SELECT id FROM whiteboard_contents 
                    WHERE session_id = ? AND is_snapshot = 0
                    ORDER BY version DESC 
                    LIMIT 30
                """, (session_id,))
                
                keep_ids = [row['id'] for row in cursor.fetchall()]
                
                if keep_ids:
                    placeholders = ','.join(['?' for _ in keep_ids])
                    cursor.execute(f"""
                        DELETE FROM whiteboard_contents 
                        WHERE session_id = ? AND is_snapshot = 0 AND id NOT IN ({placeholders})
                    """, [session_id] + keep_ids)
                    
                    total_cleaned += cursor.rowcount
            
            conn.commit()
            conn.close()
            
            return total_cleaned
            
        except Exception as e:
            print(f"清理内容版本失败: {e}")
            return 0
    
    def _optimize_database(self):
        """优化数据库"""
        try:
            conn = get_db()
            cursor = conn.cursor()
            
            # SQLite数据库优化
            cursor.execute("VACUUM")
            cursor.execute("ANALYZE")
            
            conn.commit()
            conn.close()
            
            print("数据库优化完成")
            
        except Exception as e:
            print(f"数据库优化失败: {e}")


# 全局清理任务实例
cleanup_task = WhiteboardCleanupTask()


def start_cleanup_task(app=None):
    """启动清理任务"""
    cleanup_task.start(app)


def stop_cleanup_task():
    """停止清理任务"""
    cleanup_task.stop()


if __name__ == "__main__":
    # 测试清理任务
    task = WhiteboardCleanupTask()
    task.run_cleanup()
