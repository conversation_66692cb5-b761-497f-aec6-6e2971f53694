<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>协同白板</title>
    <style>
        html, body, iframe { margin: 0; padding: 0; height: 100%; width: 100%; border: none; overflow: hidden; }
        .group-controls { position: absolute; bottom: 5rem; right: 1rem; z-index: 10; background: #fff; padding: 0.5rem 1rem; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); display: flex; align-items: center; gap: 1rem; }
        .group-controls button { padding: 0.5rem 1rem; border-radius: 5px; border: 1px solid #ccc; cursor: pointer; }
        .group-controls button:hover { background: #f0f0f0; }
        .participants-panel { position: absolute; top: 1rem; right: 1rem; z-index: 10; background: #fff; padding: 1rem; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); min-width: 200px; max-height: 300px; overflow-y: auto; }
        .participant-item { display: flex; align-items: center; gap: 0.5rem; padding: 0.25rem 0; }
        .participant-status { width: 8px; height: 8px; border-radius: 50%; }
        .participant-status.online { background: #4caf50; }
        .participant-status.offline { background: #f44336; }
        .notification { position: absolute; top: 1rem; left: 50%; transform: translateX(-50%); z-index: 20; padding: 0.75rem 1.5rem; border-radius: 4px; color: white; font-weight: 500; }
        .notification.info { background: #2196f3; }
        .notification.error { background: #f44336; }
        .notification.success { background: #4caf50; }
        .collaboration-status { position: absolute; bottom: 10rem; right: 1rem; z-index: 10; background: #fff; padding: 0.5rem 1rem; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .collaboration-status.enabled { border-left: 4px solid #4caf50; }
        .collaboration-status.disabled { border-left: 4px solid #f44336; }
    </style>
</head>
<body>
    <iframe id="excalidraw-iframe" src="{{ url_for('student.excalidraw_proxy') }}"></iframe>

    <!-- 协作状态指示器 -->
    <div id="collaboration-status" class="collaboration-status enabled">
        <span>协作模式：开启</span>
    </div>

    <!-- 参与者面板 -->
    <div id="participants-panel" class="participants-panel" style="display: none;">
        <h4 style="margin: 0 0 0.5rem 0;">在线参与者</h4>
        <div id="participants-list"></div>
    </div>

    <!-- 小组控制面板 -->
    <div class="group-controls">
        <div id="group-info">正在查找小组信息...</div>
        <button id="toggle-participants" onclick="toggleParticipants()">参与者</button>
    </div>

    <!-- 通知容器 -->
    <div id="notification-container"></div>

    <script>
        const iframe = document.getElementById('excalidraw-iframe');
        const groupInfoDiv = document.getElementById('group-info');
        const participantsPanel = document.getElementById('participants-panel');
        const participantsList = document.getElementById('participants-list');
        const collaborationStatus = document.getElementById('collaboration-status');

        let groupInfo = null;
        let isExcalidrawReady = false;
        let hasJoined = false;
        let participants = [];
        let collaborationEnabled = true;

        // 1. 尝试加入小组的中心函数
        function attemptToJoinGroup() {
            // 必须获取到小组信息、白板已准备就绪，且尚未加入
            if (groupInfo && isExcalidrawReady && !hasJoined) {
                hasJoined = true; // 防止重复加入

                // 通知iframe加入小组，传递完整的小组信息
                iframe.contentWindow.postMessage({
                    type: 'JOIN_GROUP',
                    payload: {
                        group_id: groupInfo.group_id,
                        group_name: groupInfo.group_name,
                        course_schedule_id: groupInfo.course_schedule_id,
                        student_id: '{{ session.get("student_id") }}',
                        student_name: '{{ session.get("student_name") }}'
                    }
                }, '*');

                // 更新UI状态
                groupInfoDiv.innerText = `已连接: ${groupInfo.group_name}`;
                console.log(`已自动加入协作小组: ${groupInfo.group_name} (ID: ${groupInfo.group_id})`);
            }
        }

        // 2. 获取用户小组信息
        fetch('/student/api/my_group')
            .then(res => res.json())
            .then(data => {
                if (data.success && data.group) {
                    groupInfo = data.group;
                    groupInfoDiv.innerText = `小组: ${data.group.group_name}`;
                    attemptToJoinGroup(); // 尝试加入
                } else {
                    groupInfoDiv.innerText = '未分配小组，无法协作';
                }
            })
            .catch(error => {
                console.error('获取小组信息失败:', error);
                groupInfoDiv.innerText = '获取小组信息失败';
            });

        // 3. 监听来自iframe的消息
        window.addEventListener('message', (event) => {
            // 安全检查，确保消息来自我们的iframe
            if (event.source !== iframe.contentWindow) return;

            const { type, payload } = event.data;

            switch (type) {
                case 'EXCALIDRAW_READY':
                    console.log('Excalidraw 已准备就绪');
                    isExcalidrawReady = true;
                    attemptToJoinGroup(); // 尝试加入
                    break;

                case 'EXCALIDRAW_CHANGE':
                    // 白板内容已更改，可以根据需要进行处理（例如自动保存快照）
                    // console.log('Excalidraw 内容已更改');
                    break;

                case 'PARTICIPANTS_UPDATE':
                    // 更新参与者列表
                    participants = payload.participants || [];
                    updateParticipantsList();
                    break;

                case 'COLLABORATION_STATUS':
                    // 更新协作状态
                    collaborationEnabled = payload.enabled;
                    updateCollaborationStatus();
                    break;

                case 'SHOW_NOTIFICATION':
                    // 显示通知
                    showNotification(payload.message, payload.type);
                    break;

                default:
                    // console.log('收到未知消息类型:', type);
            }
        });

        // 4. 错误处理
        iframe.onload = () => {
            console.log('Excalidraw iframe 已加载');
            groupInfoDiv.innerText = '正在等待白板初始化...';
        };

        iframe.onerror = () => {
            console.error('Excalidraw iframe 加载失败');
            groupInfoDiv.innerText = '白板加载失败，请刷新页面';
        };

        window.addEventListener('error', (event) => {
            console.error('页面错误:', event.error);
        });

        // 辅助函数
        function updateParticipantsList() {
            participantsList.innerHTML = '';
            participants.forEach(participant => {
                const item = document.createElement('div');
                item.className = 'participant-item';
                item.innerHTML = `
                    <div class="participant-status ${participant.online ? 'online' : 'offline'}"></div>
                    <span>${participant.name} (${participant.type})</span>
                `;
                participantsList.appendChild(item);
            });
        }

        function updateCollaborationStatus() {
            const statusText = collaborationEnabled ? '开启' : '关闭';
            const statusClass = collaborationEnabled ? 'enabled' : 'disabled';

            collaborationStatus.className = `collaboration-status ${statusClass}`;
            collaborationStatus.innerHTML = `<span>协作模式：${statusText}</span>`;
        }

        function toggleParticipants() {
            const isVisible = participantsPanel.style.display !== 'none';
            participantsPanel.style.display = isVisible ? 'none' : 'block';
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;

            const container = document.getElementById('notification-container');
            container.appendChild(notification);

            // 3秒后自动移除
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }
    </script>
</body>
</html>