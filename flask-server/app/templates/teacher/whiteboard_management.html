<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>白板管理</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .whiteboard-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 1rem;
            transition: box-shadow 0.3s ease;
        }
        .whiteboard-card:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .collaboration-status {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .collaboration-status.enabled {
            background-color: #d4edda;
            color: #155724;
        }
        .collaboration-status.disabled {
            background-color: #f8d7da;
            color: #721c24;
        }
        .participant-count {
            background-color: #e9ecef;
            color: #495057;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
        }
        .whiteboard-preview {
            height: 200px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            margin-bottom: 1rem;
        }
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        .btn-sm {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-chalkboard-teacher me-2"></i>白板管理</h2>
                    <div>
                        <button class="btn btn-primary" onclick="refreshWhiteboards()">
                            <i class="fas fa-sync-alt me-1"></i>刷新
                        </button>
                        <button class="btn btn-success" onclick="enableAllCollaboration()">
                            <i class="fas fa-users me-1"></i>开启全部协作
                        </button>
                        <button class="btn btn-warning" onclick="disableAllCollaboration()">
                            <i class="fas fa-ban me-1"></i>关闭全部协作
                        </button>
                    </div>
                </div>

                <!-- 白板列表 -->
                <div id="whiteboard-list" class="row">
                    <div class="col-12 text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">正在加载白板信息...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 白板详情模态框 -->
    <div class="modal fade" id="whiteboardModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">白板详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <div id="whiteboard-preview-large" class="whiteboard-preview" style="height: 400px;">
                                <i class="fas fa-chalkboard fa-3x text-muted"></i>
                                <p class="mt-2">白板预览</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6>参与者列表</h6>
                            <div id="participants-list" class="list-group">
                                <!-- 参与者列表将在这里动态生成 -->
                            </div>
                            
                            <h6 class="mt-3">操作</h6>
                            <div class="d-grid gap-2">
                                <button class="btn btn-primary" onclick="exportWhiteboard()">
                                    <i class="fas fa-download me-1"></i>导出白板
                                </button>
                                <button class="btn btn-warning" onclick="clearWhiteboard()">
                                    <i class="fas fa-eraser me-1"></i>清空白板
                                </button>
                                <button class="btn btn-info" onclick="createSnapshot()">
                                    <i class="fas fa-camera me-1"></i>创建快照
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.socket.io/4.7.5/socket.io.min.js"></script>
    <script>
        let socket;
        let whiteboards = [];
        let currentWhiteboardId = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeSocket();
            loadWhiteboards();
        });

        function initializeSocket() {
            socket = io();
            
            socket.on('connect', () => {
                console.log('已连接到服务器');
            });

            socket.on('whiteboard_collaboration_status', (data) => {
                updateWhiteboardStatus(data.session_id, data.enabled);
            });

            socket.on('whiteboard_user_joined', (data) => {
                // 更新参与者数量
                refreshWhiteboards();
            });

            socket.on('whiteboard_user_left', (data) => {
                // 更新参与者数量
                refreshWhiteboards();
            });
        }

        async function loadWhiteboards() {
            try {
                // 获取所有分组
                const groupsResponse = await fetch('/api/groups');
                const groupsData = await groupsResponse.json();
                
                if (!groupsData.success) {
                    throw new Error(groupsData.message);
                }

                whiteboards = [];
                
                // 为每个分组获取白板信息
                for (const group of groupsData.groups) {
                    try {
                        const whiteboardResponse = await fetch(`/api/groups/${group.id}/whiteboard`);
                        const whiteboardData = await whiteboardResponse.json();
                        
                        if (whiteboardData.success) {
                            whiteboards.push({
                                groupId: group.id,
                                groupName: group.group_name,
                                sessionId: whiteboardData.session_id,
                                content: whiteboardData.content,
                                participants: whiteboardData.participants,
                                collaborationEnabled: true // 默认开启
                            });
                        }
                    } catch (error) {
                        console.warn(`获取分组 ${group.id} 白板信息失败:`, error);
                    }
                }
                
                renderWhiteboards();
                
            } catch (error) {
                console.error('加载白板失败:', error);
                document.getElementById('whiteboard-list').innerHTML = `
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning"></i>
                        <p class="mt-2 text-muted">加载白板信息失败: ${error.message}</p>
                        <button class="btn btn-primary" onclick="loadWhiteboards()">重试</button>
                    </div>
                `;
            }
        }

        function renderWhiteboards() {
            const container = document.getElementById('whiteboard-list');
            
            if (whiteboards.length === 0) {
                container.innerHTML = `
                    <div class="col-12 text-center py-5">
                        <i class="fas fa-chalkboard fa-3x text-muted"></i>
                        <p class="mt-2 text-muted">暂无白板信息</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = whiteboards.map(whiteboard => `
                <div class="col-lg-6 col-xl-4">
                    <div class="whiteboard-card p-3">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="mb-0">${whiteboard.groupName}</h6>
                            <span class="collaboration-status ${whiteboard.collaborationEnabled ? 'enabled' : 'disabled'}">
                                ${whiteboard.collaborationEnabled ? '协作中' : '已暂停'}
                            </span>
                        </div>
                        
                        <div class="whiteboard-preview mb-2">
                            <i class="fas fa-chalkboard fa-2x text-muted"></i>
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="participant-count">
                                <i class="fas fa-users me-1"></i>
                                ${whiteboard.participants.length} 人在线
                            </span>
                            <small class="text-muted">会话ID: ${whiteboard.sessionId.substring(0, 8)}...</small>
                        </div>
                        
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline-primary" onclick="viewWhiteboard('${whiteboard.sessionId}')">
                                <i class="fas fa-eye me-1"></i>查看
                            </button>
                            <button class="btn btn-sm btn-outline-${whiteboard.collaborationEnabled ? 'warning' : 'success'}" 
                                    onclick="toggleCollaboration('${whiteboard.sessionId}', ${!whiteboard.collaborationEnabled})">
                                <i class="fas fa-${whiteboard.collaborationEnabled ? 'pause' : 'play'} me-1"></i>
                                ${whiteboard.collaborationEnabled ? '暂停' : '开启'}
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="exportWhiteboardContent('${whiteboard.sessionId}')">
                                <i class="fas fa-download me-1"></i>导出
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function refreshWhiteboards() {
            loadWhiteboards();
        }

        async function toggleCollaboration(sessionId, enabled) {
            try {
                const response = await fetch(`/api/whiteboard/sessions/${sessionId}/collaboration`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ enabled })
                });

                const data = await response.json();
                if (data.success) {
                    // 更新本地状态
                    const whiteboard = whiteboards.find(w => w.sessionId === sessionId);
                    if (whiteboard) {
                        whiteboard.collaborationEnabled = enabled;
                        renderWhiteboards();
                    }
                } else {
                    alert('操作失败: ' + data.message);
                }
            } catch (error) {
                console.error('切换协作状态失败:', error);
                alert('操作失败: ' + error.message);
            }
        }

        function updateWhiteboardStatus(sessionId, enabled) {
            const whiteboard = whiteboards.find(w => w.sessionId === sessionId);
            if (whiteboard) {
                whiteboard.collaborationEnabled = enabled;
                renderWhiteboards();
            }
        }

        async function enableAllCollaboration() {
            for (const whiteboard of whiteboards) {
                if (!whiteboard.collaborationEnabled) {
                    await toggleCollaboration(whiteboard.sessionId, true);
                }
            }
        }

        async function disableAllCollaboration() {
            for (const whiteboard of whiteboards) {
                if (whiteboard.collaborationEnabled) {
                    await toggleCollaboration(whiteboard.sessionId, false);
                }
            }
        }

        function viewWhiteboard(sessionId) {
            currentWhiteboardId = sessionId;
            const whiteboard = whiteboards.find(w => w.sessionId === sessionId);
            
            if (whiteboard) {
                // 更新参与者列表
                const participantsList = document.getElementById('participants-list');
                participantsList.innerHTML = whiteboard.participants.map(p => `
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${p.user_name}</strong>
                            <small class="text-muted d-block">${p.user_type}</small>
                        </div>
                        <span class="badge bg-${p.is_online ? 'success' : 'secondary'}">
                            ${p.is_online ? '在线' : '离线'}
                        </span>
                    </div>
                `).join('');
                
                // 显示模态框
                new bootstrap.Modal(document.getElementById('whiteboardModal')).show();
            }
        }

        function exportWhiteboardContent(sessionId) {
            const whiteboard = whiteboards.find(w => w.sessionId === sessionId);
            if (whiteboard && whiteboard.content) {
                const dataStr = JSON.stringify(whiteboard.content, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `whiteboard_${whiteboard.groupName}_${new Date().toISOString().slice(0, 19)}.json`;
                link.click();
            }
        }

        function exportWhiteboard() {
            if (currentWhiteboardId) {
                exportWhiteboardContent(currentWhiteboardId);
            }
        }

        function clearWhiteboard() {
            if (currentWhiteboardId && confirm('确定要清空这个白板吗？此操作不可撤销。')) {
                // TODO: 实现清空白板功能
                alert('清空白板功能待实现');
            }
        }

        function createSnapshot() {
            if (currentWhiteboardId) {
                // TODO: 实现创建快照功能
                alert('创建快照功能待实现');
            }
        }
    </script>
</body>
</html>
